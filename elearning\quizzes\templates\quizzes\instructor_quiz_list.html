{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
  .quiz-list-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
  }

  .quiz-header {
    background: linear-gradient(135deg, #C8A8E9 0%, #A8D0F0 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .quiz-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .quiz-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
  }

  .btn-primary {
    background: linear-gradient(135deg, #6B46C1, #9333EA);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(107, 70, 193, 0.3);
    color: white;
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6B7280, #4B5563);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(107, 114, 128, 0.3);
    color: white;
  }

  .quiz-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .quiz-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #E5E7EB;
  }

  .quiz-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  }

  .quiz-card-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .quiz-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.5rem;
    flex: 1;
  }

  .quiz-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .status-published {
    background: #D1FAE5;
    color: #065F46;
  }

  .status-draft {
    background: #FEF3C7;
    color: #92400E;
  }

  .quiz-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6B7280;
    font-size: 0.875rem;
  }

  .quiz-description {
    color: #6B7280;
    margin-bottom: 1.5rem;
    line-height: 1.5;
  }

  .quiz-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
  }

  .btn-outline-primary {
    border: 2px solid #6B46C1;
    color: #6B46C1;
    background: transparent;
  }

  .btn-outline-primary:hover {
    background: #6B46C1;
    color: white;
  }

  .btn-outline-success {
    border: 2px solid #059669;
    color: #059669;
    background: transparent;
  }

  .btn-outline-success:hover {
    background: #059669;
    color: white;
  }

  .btn-outline-warning {
    border: 2px solid #D97706;
    color: #D97706;
    background: transparent;
  }

  .btn-outline-warning:hover {
    background: #D97706;
    color: white;
  }

  .btn-outline-danger {
    border: 2px solid #DC2626;
    color: #DC2626;
    background: transparent;
  }

  .btn-outline-danger:hover {
    background: #DC2626;
    color: white;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6B7280;
  }

  .empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #374151;
  }

  .empty-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  @media (max-width: 768px) {
    .quiz-list-container {
      padding: 1rem;
    }
    
    .quiz-grid {
      grid-template-columns: 1fr;
    }
    
    .action-buttons {
      flex-direction: column;
      align-items: center;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="quiz-list-container">
  <div class="quiz-header">
    <h1 class="quiz-title">
      <i class="fas fa-question-circle"></i> Mes Quiz
    </h1>
    <p class="quiz-subtitle">Gérez vos quiz et suivez les performances de vos étudiants</p>
  </div>

  <div class="action-buttons">
    <a href="{% url 'instructor_dashboard' %}" class="btn-secondary">
      <i class="fas fa-arrow-left"></i>
      Retour au Dashboard
    </a>
    <a href="{% url 'quizzes:select_course_for_quiz' %}" class="btn-primary">
      <i class="fas fa-plus"></i>
      Créer un quiz
    </a>
  </div>

  {% if quizzes %}
    <div class="quiz-grid">
      {% for quiz in quizzes %}
      <div class="quiz-card">
        <div class="quiz-card-header">
          <div>
            <h3 class="quiz-card-title">{{ quiz.title }}</h3>
            <span class="quiz-status status-{{ quiz.is_published|yesno:'published,draft' }}">
              {{ quiz.is_published|yesno:'Publié,Brouillon' }}
            </span>
          </div>
        </div>

        <div class="quiz-meta">
          <div class="meta-item">
            <i class="fas fa-book"></i>
            <span>{{ quiz.course.title }}</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-list"></i>
            <span>{{ quiz.questions.count }} questions</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-users"></i>
            <span>{{ quiz.submissions.count }} tentatives</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-clock"></i>
            <span>{{ quiz.time_limit|default:"Illimité" }}{% if quiz.time_limit %} min{% endif %}</span>
          </div>
        </div>

        {% if quiz.description %}
        <p class="quiz-description">{{ quiz.description|truncatewords:15 }}</p>
        {% endif %}

        <div class="quiz-actions">
          <a href="{% url 'quizzes:edit_quiz' quiz.id %}" class="btn-sm btn-outline-primary" title="Modifier le quiz">
            <i class="fas fa-edit"></i>
            Modifier
          </a>
          <a href="{% url 'quizzes:add_question' quiz.id %}" class="btn-sm btn-outline-success" title="Ajouter une question">
            <i class="fas fa-plus"></i>
            Questions
          </a>
          {% if quiz.is_published %}
          <a href="{% url 'quizzes:take_quiz' quiz.id %}" class="btn-sm btn-outline-warning" title="Prévisualiser le quiz" target="_blank">
            <i class="fas fa-eye"></i>
            Prévisualiser
          </a>
          {% endif %}
        </div>
      </div>
      {% endfor %}
    </div>
  {% else %}
    <div class="empty-state">
      <i class="fas fa-question-circle"></i>
      <h3>Aucun quiz créé</h3>
      <p>Commencez par créer votre premier quiz pour évaluer vos étudiants</p>
      <a href="{% url 'quizzes:select_course_for_quiz' %}" class="btn-primary">
        <i class="fas fa-plus"></i>
        Créer un quiz
      </a>
    </div>
  {% endif %}
</div>
{% endblock %}
