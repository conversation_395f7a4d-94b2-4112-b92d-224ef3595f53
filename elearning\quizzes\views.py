from django.shortcuts import render, get_object_or_404, redirect
from .models import Quiz, Question, QuizSubmission
from django.contrib.auth.decorators import login_required

@login_required
def take_quiz(request, quiz_id):
    quiz = get_object_or_404(Quiz, id=quiz_id)
    questions = quiz.question_set.all()

    if request.method == 'POST':
        score = 0
        for question in questions:
            selected = request.POST.get(str(question.id))
            if selected == question.correct_option:
                score += 1
        QuizSubmission.objects.create(
            quiz=quiz,
            student=request.user,
            score=score
        )
        return render(request, 'quizzes/result.html', {'score': score, 'total': len(questions)})

    return render(request, 'quizzes/take_quiz.html', {'quiz': quiz, 'questions': questions})
