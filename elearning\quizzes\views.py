from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Q, Count, Avg
from datetime import <PERSON><PERSON><PERSON>
import json

from .models import Quiz, Question, QuizSubmission, QuizAnswer
from courses.models import Course
from users.models import User


def is_instructor(user):
    return user.is_authenticated and user.role == 'instructor'


def is_student(user):
    return user.is_authenticated and user.role == 'student'


# ===== VUES POUR LES INSTRUCTEURS =====

@login_required
@user_passes_test(is_instructor)
def instructor_quiz_list(request):
    """Liste des quiz créés par l'instructeur"""
    quizzes = Quiz.objects.filter(created_by=request.user).order_by('-created_at')

    context = {
        'quizzes': quizzes,
        'title': 'Mes Quiz'
    }
    return render(request, 'quizzes/instructor_quiz_list.html', context)


@login_required
@user_passes_test(is_instructor)
def create_quiz(request, course_id):
    """Créer un nouveau quiz"""
    course = get_object_or_404(Course, id=course_id, instructor=request.user)

    if request.method == 'POST':
        title = request.POST.get('title')
        description = request.POST.get('description', '')
        correction_type = request.POST.get('correction_type', 'auto')
        time_limit = request.POST.get('time_limit')
        max_attempts = request.POST.get('max_attempts', 1)
        passing_score = request.POST.get('passing_score', 60)

        try:
            quiz = Quiz.objects.create(
                course=course,
                title=title,
                description=description,
                correction_type=correction_type,
                time_limit=int(time_limit) if time_limit else None,
                max_attempts=int(max_attempts),
                passing_score=int(passing_score),
                created_by=request.user
            )

            messages.success(request, f"Quiz '{title}' créé avec succès!")
            return redirect('quizzes:edit_quiz', quiz_id=quiz.id)

        except Exception as e:
            messages.error(request, f"Erreur lors de la création du quiz: {str(e)}")

    context = {
        'course': course,
        'title': f'Créer un quiz - {course.title}'
    }
    return render(request, 'quizzes/create_quiz.html', context)


@login_required
@user_passes_test(is_instructor)
def edit_quiz(request, quiz_id):
    """Modifier un quiz et ses questions"""
    quiz = get_object_or_404(Quiz, id=quiz_id, created_by=request.user)
    questions = quiz.questions.all().order_by('order')

    if request.method == 'POST':
        # Mise à jour des informations du quiz
        quiz.title = request.POST.get('title', quiz.title)
        quiz.description = request.POST.get('description', quiz.description)
        quiz.correction_type = request.POST.get('correction_type', quiz.correction_type)
        quiz.time_limit = int(request.POST.get('time_limit')) if request.POST.get('time_limit') else None
        quiz.max_attempts = int(request.POST.get('max_attempts', quiz.max_attempts))
        quiz.passing_score = int(request.POST.get('passing_score', quiz.passing_score))
        quiz.is_published = request.POST.get('is_published') == 'on'
        quiz.save()

        messages.success(request, "Quiz mis à jour avec succès!")
        return redirect('quizzes:edit_quiz', quiz_id=quiz.id)

    context = {
        'quiz': quiz,
        'questions': questions,
        'title': f'Modifier - {quiz.title}'
    }
    return render(request, 'quizzes/edit_quiz.html', context)


@login_required
@user_passes_test(is_instructor)
def add_question(request, quiz_id):
    """Ajouter une question à un quiz"""
    quiz = get_object_or_404(Quiz, id=quiz_id, created_by=request.user)

    if request.method == 'POST':
        question_type = request.POST.get('question_type', 'multiple_choice')
        text = request.POST.get('text')
        points = int(request.POST.get('points', 1))
        explanation = request.POST.get('explanation', '')

        # Déterminer l'ordre de la question
        last_question = quiz.questions.order_by('-order').first()
        order = (last_question.order + 1) if last_question else 1

        question = Question.objects.create(
            quiz=quiz,
            question_type=question_type,
            text=text,
            points=points,
            order=order,
            explanation=explanation
        )

        # Traiter les options selon le type de question
        if question_type == 'multiple_choice':
            question.option_a = request.POST.get('option_a', '')
            question.option_b = request.POST.get('option_b', '')
            question.option_c = request.POST.get('option_c', '')
            question.option_d = request.POST.get('option_d', '')
            question.correct_option = request.POST.get('correct_option', 'A')
        elif question_type == 'true_false':
            question.correct_answer_boolean = request.POST.get('correct_answer_boolean') == 'true'
        elif question_type in ['short_answer', 'essay']:
            question.correct_answer_text = request.POST.get('correct_answer_text', '')

        question.save()

        messages.success(request, "Question ajoutée avec succès!")
        return redirect('quizzes:edit_quiz', quiz_id=quiz.id)

    context = {
        'quiz': quiz,
        'title': f'Ajouter une question - {quiz.title}'
    }
    return render(request, 'quizzes/add_question.html', context)


# ===== VUES POUR LES ÉTUDIANTS =====

@login_required
@user_passes_test(is_student)
def take_quiz(request, quiz_id):
    """Passer un quiz"""
    quiz = get_object_or_404(Quiz, id=quiz_id, is_published=True)

    # Vérifier si l'étudiant est inscrit au cours
    if not quiz.course.students.filter(id=request.user.id).exists():
        messages.error(request, "Vous devez être inscrit au cours pour passer ce quiz.")
        return redirect('course_detail_public', course_id=quiz.course.id)

    # Vérifier le nombre de tentatives
    attempts_count = QuizSubmission.objects.filter(quiz=quiz, student=request.user).count()
    if attempts_count >= quiz.max_attempts:
        messages.error(request, f"Vous avez déjà utilisé toutes vos tentatives ({quiz.max_attempts}).")
        return redirect('quiz_results', quiz_id=quiz.id)

    # Créer ou récupérer une soumission en cours
    submission, created = QuizSubmission.objects.get_or_create(
        quiz=quiz,
        student=request.user,
        attempt_number=attempts_count + 1,
        defaults={'status': 'in_progress', 'max_score': quiz.get_max_score()}
    )

    questions = quiz.questions.all().order_by('order')

    if request.method == 'POST':
        # Traitement de la soumission
        submission.submitted_at = timezone.now()
        submission.status = 'submitted'

        total_score = 0

        for question in questions:
            answer, created = QuizAnswer.objects.get_or_create(
                submission=submission,
                question=question
            )

            # Enregistrer la réponse selon le type de question
            if question.is_multiple_choice():
                answer.selected_option = request.POST.get(f'question_{question.id}', '')
            elif question.is_true_false():
                answer.boolean_answer = request.POST.get(f'question_{question.id}') == 'true'
            elif question.is_short_answer() or question.is_essay():
                answer.text_answer = request.POST.get(f'question_{question.id}', '')

            answer.save()

            # Correction automatique si possible
            if quiz.is_auto_corrected() and not question.requires_manual_correction():
                if answer.auto_grade():
                    total_score += answer.points_awarded

        submission.score = total_score
        submission.calculate_percentage()

        if quiz.is_auto_corrected():
            submission.status = 'graded'
            submission.graded_at = timezone.now()

        submission.save()

        messages.success(request, "Quiz soumis avec succès!")
        return redirect('quiz_results', quiz_id=quiz.id)

    context = {
        'quiz': quiz,
        'questions': questions,
        'submission': submission,
        'title': f'Quiz - {quiz.title}'
    }
    return render(request, 'quizzes/take_quiz.html', context)


@login_required
def quiz_results(request, quiz_id):
    """Afficher les résultats d'un quiz"""
    quiz = get_object_or_404(Quiz, id=quiz_id)

    if request.user.role == 'student':
        submissions = QuizSubmission.objects.filter(
            quiz=quiz,
            student=request.user
        ).order_by('-attempt_number')
    else:
        # Pour les instructeurs, afficher toutes les soumissions
        submissions = QuizSubmission.objects.filter(quiz=quiz).order_by('-submitted_at')

    context = {
        'quiz': quiz,
        'submissions': submissions,
        'title': f'Résultats - {quiz.title}'
    }
    return render(request, 'quizzes/quiz_results.html', context)
