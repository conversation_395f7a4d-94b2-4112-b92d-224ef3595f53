"""
URL configuration for elearning project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import render
from courses import admin_views, course_views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('users/', include('users.urls')),
    path('courses/', include([
        path('', course_views.course_list, name='course_list'),
        path('add/', course_views.add_course, name='add_course'),
        path('<int:course_id>/', course_views.course_detail, name='course_detail_by_id'),
        path('<int:course_id>/edit/', course_views.edit_course, name='edit_course'),
        path('<int:course_id>/delete/', course_views.delete_course, name='delete_course'),
        path('<int:course_id>/enroll/', course_views.enroll_course, name='enroll_course'),
    ])),
    path('quizzes/', include('quizzes.urls')),
    path('analytics/', include('analytics.urls')),
    path('payments/', include('payments.urls')),
    path('', lambda request: render(request, 'home.html'), name='home'),
    
    # URLs pour l'administration personnalisée
    path('admin-dashboard/', admin_views.admin_dashboard, name='admin_dashboard'),
    path('admin-dashboard/users/', admin_views.admin_students, name='admin_users'),  # Utiliser admin_students au lieu de user_list
    path('admin-dashboard/instructors/', admin_views.admin_instructors, name='admin_instructors'),
    path('admin-dashboard/courses/', admin_views.course_list, name='admin_courses'),
    path('admin-dashboard/courses/create/', admin_views.create_course, name='create_course'),
    path('admin-dashboard/courses/<int:course_id>/update/', admin_views.update_course, name='update_course'),
    path('admin-dashboard/courses/<int:course_id>/delete/', admin_views.delete_course, name='delete_course'),
    path('admin-dashboard/modules/create/', admin_views.create_module, name='create_module'),
    path('admin-dashboard/modules/<int:module_id>/update/', admin_views.update_module, name='update_module'),
    path('admin-dashboard/modules/<int:module_id>/delete/', admin_views.delete_module, name='delete_module'),
    path('admin-dashboard/lessons/create/', admin_views.create_lesson, name='create_lesson'),
    path('admin-dashboard/lessons/<int:lesson_id>/update/', admin_views.update_lesson, name='update_lesson'),
    path('admin-dashboard/lessons/<int:lesson_id>/delete/', admin_views.delete_lesson, name='delete_lesson'),
    path('admin-dashboard/settings/', admin_views.settings, name='admin_settings'),
    path('admin-dashboard/instructors/<int:instructor_id>/delete/', admin_views.delete_instructor, name='delete_instructor'),
    path('admin-dashboard/user-roles/', admin_views.manage_user_roles, name='manage_user_roles'),
    path('admin-dashboard/bulk-actions/', admin_views.bulk_user_actions, name='bulk_user_actions'),

    # URLs pour les instructeurs
    path('instructor-dashboard/', lambda request: __import__('courses.instructor_views', fromlist=['instructor_dashboard']).instructor_dashboard(request), name='instructor_dashboard'),
    path('instructor/courses/', lambda request: __import__('courses.instructor_views', fromlist=['my_courses']).my_courses(request), name='instructor_my_courses'),
    path('instructor/courses/create/', lambda request: __import__('courses.instructor_views', fromlist=['create_course']).create_course(request), name='instructor_create_course'),
    path('instructor/courses/<int:course_id>/', lambda request, course_id: __import__('courses.instructor_views', fromlist=['course_detail']).course_detail(request, course_id), name='instructor_course_detail'),
    path('instructor/courses/<int:course_id>/edit/', lambda request, course_id: __import__('courses.instructor_views', fromlist=['edit_course']).edit_course(request, course_id), name='instructor_edit_course'),
    path('instructor/courses/<int:course_id>/delete/', lambda request, course_id: __import__('courses.instructor_views', fromlist=['delete_course']).delete_course(request, course_id), name='instructor_delete_course'),
    path('instructor/courses/<int:course_id>/students/', lambda request, course_id: __import__('courses.instructor_views', fromlist=['course_students']).course_students(request, course_id), name='instructor_course_students'),
    path('instructor/courses/<int:course_id>/toggle-status/', lambda request, course_id: __import__('courses.instructor_views', fromlist=['toggle_course_status']).toggle_course_status(request, course_id), name='instructor_toggle_course_status'),

    # URLs pour la gestion des modules
    path('instructor/courses/<int:course_id>/modules/create/', lambda request, course_id: __import__('courses.instructor_views', fromlist=['create_module']).create_module(request, course_id), name='instructor_create_module'),
    path('instructor/courses/<int:course_id>/modules/<int:module_id>/edit/', lambda request, course_id, module_id: __import__('courses.instructor_views', fromlist=['edit_module']).edit_module(request, course_id, module_id), name='instructor_edit_module'),
    path('instructor/courses/<int:course_id>/modules/<int:module_id>/delete/', lambda request, course_id, module_id: __import__('courses.instructor_views', fromlist=['delete_module']).delete_module(request, course_id, module_id), name='instructor_delete_module'),

    # URLs pour la gestion des leçons
    path('instructor/courses/<int:course_id>/modules/<int:module_id>/lessons/create/', lambda request, course_id, module_id: __import__('courses.instructor_views', fromlist=['create_lesson']).create_lesson(request, course_id, module_id), name='instructor_create_lesson'),
    path('instructor/courses/<int:course_id>/modules/<int:module_id>/lessons/<int:lesson_id>/edit/', lambda request, course_id, module_id, lesson_id: __import__('courses.instructor_views', fromlist=['edit_lesson']).edit_lesson(request, course_id, module_id, lesson_id), name='instructor_edit_lesson'),
    path('instructor/courses/<int:course_id>/modules/<int:module_id>/lessons/<int:lesson_id>/delete/', lambda request, course_id, module_id, lesson_id: __import__('courses.instructor_views', fromlist=['delete_lesson']).delete_lesson(request, course_id, module_id, lesson_id), name='instructor_delete_lesson'),

    # URLs pour les étudiants
    path('student-dashboard/', lambda request: __import__('courses.student_views', fromlist=['student_dashboard']).student_dashboard(request), name='student_dashboard'),
    path('catalog/', lambda request: __import__('courses.student_views', fromlist=['course_catalog']).course_catalog(request), name='course_catalog'),
    path('course/<int:course_id>/', lambda request, course_id: __import__('courses.student_views', fromlist=['course_detail_public']).course_detail_public(request, course_id), name='course_detail_public'),
    path('course/<int:course_id>/enroll/', lambda request, course_id: __import__('courses.student_views', fromlist=['enroll_course']).enroll_course(request, course_id), name='enroll_course'),
    path('my-courses/', lambda request: __import__('courses.student_views', fromlist=['my_courses']).my_courses(request), name='student_my_courses'),
    path('course/<int:course_id>/learn/', lambda request, course_id: __import__('courses.student_views', fromlist=['course_learn']).course_learn(request, course_id), name='course_learn'),
    path('course/<int:course_id>/lesson/<int:lesson_id>/', lambda request, course_id, lesson_id: __import__('courses.student_views', fromlist=['lesson_view']).lesson_view(request, course_id, lesson_id), name='lesson_view'),
    path('course/<int:course_id>/lesson/<int:lesson_id>/complete/', lambda request, course_id, lesson_id: __import__('courses.student_views', fromlist=['mark_lesson_complete']).mark_lesson_complete(request, course_id, lesson_id), name='mark_lesson_complete'),
    path('course/<int:course_id>/lesson/<int:lesson_id>/download/', lambda request, course_id, lesson_id: __import__('courses.student_views', fromlist=['download_file']).download_file(request, course_id, lesson_id), name='download_file'),
]

# Ajoutez ces URLs pour les pages manquantes
urlpatterns += [
    path('about/', lambda request: render(request, 'about.html'), name='about'),
    path('contact/', lambda request: render(request, 'contact.html'), name='contact'),
    path('profile/', lambda request: render(request, 'users/profile.html'), name='profile'),
    path('my-courses/', lambda request: render(request, 'courses/my_courses.html'), name='my_courses'),
# Duplication supprimée - instructor_dashboard déjà défini ligne 59
]

# URL pour l'administration supprimée (duplication)

# Servir les fichiers média en développement
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
