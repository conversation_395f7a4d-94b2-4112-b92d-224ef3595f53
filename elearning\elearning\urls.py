"""
URL configuration for elearning project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import render
from courses import admin_views, course_views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('users/', include('users.urls')),
    path('courses/', include([
        path('', course_views.course_list, name='course_list'),
        path('add/', course_views.add_course, name='add_course'),
        path('<int:course_id>/', course_views.course_detail, name='course_detail_by_id'),
        path('<int:course_id>/edit/', course_views.edit_course, name='edit_course'),
        path('<int:course_id>/delete/', course_views.delete_course, name='delete_course'),
        path('<int:course_id>/enroll/', course_views.enroll_course, name='enroll_course'),
    ])),
    path('quizzes/', include('quizzes.urls')),
    path('', lambda request: render(request, 'home.html'), name='home'),
    
    # URLs pour l'administration personnalisée
    path('admin-dashboard/', admin_views.admin_dashboard, name='admin_dashboard'),
    path('admin-dashboard/users/', admin_views.admin_students, name='admin_users'),  # Utiliser admin_students au lieu de user_list
    path('admin-dashboard/instructors/', admin_views.admin_instructors, name='admin_instructors'),
    path('admin-dashboard/courses/', admin_views.course_list, name='admin_courses'),
    path('admin-dashboard/courses/create/', admin_views.create_course, name='create_course'),
    path('admin-dashboard/courses/<int:course_id>/update/', admin_views.update_course, name='update_course'),
    path('admin-dashboard/courses/<int:course_id>/delete/', admin_views.delete_course, name='delete_course'),
    path('admin-dashboard/modules/create/', admin_views.create_module, name='create_module'),
    path('admin-dashboard/modules/<int:module_id>/update/', admin_views.update_module, name='update_module'),
    path('admin-dashboard/modules/<int:module_id>/delete/', admin_views.delete_module, name='delete_module'),
    path('admin-dashboard/lessons/create/', admin_views.create_lesson, name='create_lesson'),
    path('admin-dashboard/lessons/<int:lesson_id>/update/', admin_views.update_lesson, name='update_lesson'),
    path('admin-dashboard/lessons/<int:lesson_id>/delete/', admin_views.delete_lesson, name='delete_lesson'),
    path('admin-dashboard/settings/', admin_views.settings, name='admin_settings'),
    path('admin-dashboard/instructors/<int:instructor_id>/delete/', admin_views.delete_instructor, name='delete_instructor'),
]

# Ajoutez ces URLs pour les pages manquantes
urlpatterns += [
    path('about/', lambda request: render(request, 'about.html'), name='about'),
    path('contact/', lambda request: render(request, 'contact.html'), name='contact'),
    path('profile/', lambda request: render(request, 'users/profile.html'), name='profile'),
    path('my-courses/', lambda request: render(request, 'courses/my_courses.html'), name='my_courses'),
    path('instructor-dashboard/', lambda request: render(request, 'courses/instructor_dashboard.html'), name='instructor_dashboard'),
]

# Ajoutez également cette URL pour l'administration
urlpatterns += [
    path('admin-dashboard/', lambda request: render(request, 'admin/dashboard.html'), name='admin_dashboard'),
]

# Servir les fichiers média en développement
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
