{% extends 'base.html' %}
{% load static %}

{% block title %}Gestion des Étudiants | E-Learn+{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <!-- Sidebar de navigation -->
    <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
      <div class="position-sticky pt-3">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'dashboard' %}active{% endif %}" href="{% url 'admin_dashboard' %}">
              <i class="fas fa-tachometer-alt me-2"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'students' %}active{% endif %}" href="{% url 'admin_users' %}">
              <i class="fas fa-user-graduate me-2"></i>
              Étudiants
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'instructors' %}active{% endif %}" href="{% url 'admin_instructors' %}">
              <i class="fas fa-chalkboard-teacher me-2"></i>
              Instructeurs
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'courses' %}active{% endif %}" href="{% url 'admin_courses' %}">
              <i class="fas fa-book me-2"></i>
              Cours
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'settings' %}active{% endif %}" href="{% url 'admin_settings' %}">
              <i class="fas fa-cog me-2"></i>
              Paramètres
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Contenu principal -->
    <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
      <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Gestion des Étudiants</h1>
      </div>

      <!-- Liste des étudiants -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
          <h5 class="mb-0">Liste des étudiants</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Nom d'utilisateur</th>
                  <th>Email</th>
                  <th>Date d'inscription</th>
                  <th>Cours suivis</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for student in students %}
                <tr>
                  <td>{{ student.username }}</td>
                  <td>{{ student.email }}</td>
                  <td>{{ student.date_joined|date:"d/m/Y" }}</td>
                  <td>{{ student.courses.count }}</td>
                  <td>
                    <div class="btn-group">
                      <a href="#" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="#" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-trash"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="5" class="text-center py-3">
                    <p class="text-muted mb-0">Aucun étudiant trouvé</p>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>
{% endblock %}