from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.db.models import Count, Q, Avg
from django.http import JsonResponse, HttpResponse, Http404
from django.utils import timezone
from datetime import timedelta
from django.core.paginator import Paginator

from users.models import User
from .models import Course, Module, Lesson, Progress
try:
    from messaging.models import Message
except ImportError:
    Message = None

def is_student(user):
    return user.is_authenticated and user.role == 'student'

@login_required
@user_passes_test(is_student)
def student_dashboard(request):
    """Dashboard principal pour les étudiants"""
    student = request.user
    
    # Cours auxquels l'étudiant est inscrit
    enrolled_courses = student.enrolled_courses.all()
    
    # Statistiques de l'étudiant
    total_enrolled = enrolled_courses.count()
    completed_courses = 0
    total_progress = 0
    
    # Calculer la progression pour chaque cours
    course_progress = []
    for course in enrolled_courses:
        progress_rate = course.get_completion_rate(student)
        total_progress += progress_rate
        if progress_rate >= 100:
            completed_courses += 1
        
        course_progress.append({
            'course': course,
            'progress': progress_rate,
            'completed_lessons': Progress.objects.filter(
                student=student,
                course=course,
                completed=True
            ).count()
        })
    
    # Progression moyenne
    avg_progress = total_progress / total_enrolled if total_enrolled > 0 else 0
    
    # Cours recommandés (non inscrits)
    recommended_courses = Course.objects.filter(
        is_published=True
    ).exclude(
        students=student
    ).annotate(
        student_count=Count('students')
    ).order_by('-student_count')[:4]
    
    # Leçons récentes
    recent_lessons = Progress.objects.filter(
        student=student,
        completed=True
    ).order_by('-completed_at')[:5]
    
    # Messages récents
    recent_messages = []
    if Message:
        recent_messages = Message.objects.filter(
            recipient=student
        ).order_by('-created_at')[:5]
    
    context = {
        'total_enrolled': total_enrolled,
        'completed_courses': completed_courses,
        'avg_progress': round(avg_progress, 1),
        'course_progress': course_progress,
        'recommended_courses': recommended_courses,
        'recent_lessons': recent_lessons,
        'recent_messages': recent_messages,
    }
    
    return render(request, 'courses/student_dashboard.html', context)

def course_catalog(request):
    """Catalogue des cours disponibles"""
    courses = Course.objects.filter(is_published=True)
    
    # Filtres
    category = request.GET.get('category')
    level = request.GET.get('level')
    search = request.GET.get('search')
    
    if category:
        courses = courses.filter(category=category)
    
    if level:
        courses = courses.filter(level=level)
    
    if search:
        courses = courses.filter(
            Q(title__icontains=search) | 
            Q(description__icontains=search)
        )
    
    # Ajouter les statistiques pour chaque cours
    for course in courses:
        course.student_count = course.students.count()
        course.lesson_count = sum(module.lessons.count() for module in course.modules.all())
        if request.user.is_authenticated:
            course.is_enrolled = course.students.filter(id=request.user.id).exists()
        else:
            course.is_enrolled = False
    
    # Pagination
    paginator = Paginator(courses, 9)  # 9 cours par page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Choix pour les filtres
    category_choices = Course.CATEGORY_CHOICES
    level_choices = Course.LEVEL_CHOICES
    
    context = {
        'page_obj': page_obj,
        'category_choices': category_choices,
        'level_choices': level_choices,
        'current_category': category,
        'current_level': level,
        'current_search': search,
    }
    
    return render(request, 'courses/course_catalog.html', context)

def course_detail_public(request, course_id):
    """Détails publics d'un cours"""
    course = get_object_or_404(Course, id=course_id, is_published=True)
    
    # Statistiques du cours
    student_count = course.students.count()
    modules = course.modules.all().order_by('order')
    lesson_count = sum(module.lessons.count() for module in modules)
    
    # Vérifier si l'utilisateur est inscrit
    is_enrolled = False
    user_progress = None
    if request.user.is_authenticated:
        is_enrolled = course.students.filter(id=request.user.id).exists()
        if is_enrolled:
            user_progress = course.get_completion_rate(request.user)
    
    context = {
        'course': course,
        'modules': modules,
        'student_count': student_count,
        'lesson_count': lesson_count,
        'is_enrolled': is_enrolled,
        'user_progress': user_progress,
    }
    
    return render(request, 'courses/course_detail_public.html', context)

@login_required
@user_passes_test(is_student)
def enroll_course(request, course_id):
    """Inscription à un cours"""
    course = get_object_or_404(Course, id=course_id, is_published=True)
    student = request.user
    
    if course.students.filter(id=student.id).exists():
        messages.warning(request, f"Vous êtes déjà inscrit au cours '{course.title}'.")
    else:
        course.students.add(student)
        messages.success(request, f"Inscription réussie au cours '{course.title}' !")
    
    return redirect('course_detail_public', course_id=course.id)

@login_required
@user_passes_test(is_student)
def my_courses(request):
    """Mes cours (étudiants inscrits)"""
    student = request.user
    enrolled_courses = student.enrolled_courses.all()
    
    # Ajouter les statistiques de progression pour chaque cours
    course_data = []
    for course in enrolled_courses:
        progress_rate = course.get_completion_rate(student)
        completed_lessons = Progress.objects.filter(
            student=student,
            course=course,
            completed=True
        ).count()
        
        total_lessons = sum(module.lessons.count() for module in course.modules.all())
        
        course_data.append({
            'course': course,
            'progress_rate': progress_rate,
            'completed_lessons': completed_lessons,
            'total_lessons': total_lessons,
        })
    
    context = {
        'course_data': course_data,
    }
    
    return render(request, 'courses/student_my_courses.html', context)

@login_required
@user_passes_test(is_student)
def course_learn(request, course_id):
    """Interface d'apprentissage d'un cours"""
    course = get_object_or_404(Course, id=course_id, is_published=True)
    student = request.user
    
    # Vérifier que l'étudiant est inscrit
    if not course.students.filter(id=student.id).exists():
        messages.error(request, "Vous devez être inscrit à ce cours pour y accéder.")
        return redirect('course_detail_public', course_id=course.id)
    
    # Récupérer tous les modules et leçons
    modules = course.modules.all().order_by('order')
    
    # Ajouter les informations de progression pour chaque leçon
    for module in modules:
        lessons = module.lessons.all().order_by('order')
        for lesson in lessons:
            try:
                progress = Progress.objects.get(
                    student=student,
                    course=course,
                    lesson=lesson
                )
                lesson.is_completed = progress.completed
                lesson.completed_at = progress.completed_at
            except Progress.DoesNotExist:
                lesson.is_completed = False
                lesson.completed_at = None
        module.lessons_with_progress = lessons
    
    # Progression globale
    progress_rate = course.get_completion_rate(student)
    
    context = {
        'course': course,
        'modules': modules,
        'progress_rate': progress_rate,
    }
    
    return render(request, 'courses/course_learn.html', context)

@login_required
@user_passes_test(is_student)
def lesson_view(request, course_id, lesson_id):
    """Vue d'une leçon spécifique"""
    course = get_object_or_404(Course, id=course_id, is_published=True)
    lesson = get_object_or_404(Lesson, id=lesson_id)
    student = request.user
    
    # Vérifier que l'étudiant est inscrit
    if not course.students.filter(id=student.id).exists():
        messages.error(request, "Vous devez être inscrit à ce cours pour y accéder.")
        return redirect('course_detail_public', course_id=course.id)
    
    # Vérifier que la leçon appartient au cours
    if lesson.module.course != course:
        raise Http404("Cette leçon n'appartient pas à ce cours.")
    
    # Récupérer ou créer le progress
    progress, _ = Progress.objects.get_or_create(
        student=student,
        course=course,
        lesson=lesson,
        defaults={'completed': False}
    )
    
    # Navigation (leçon précédente/suivante)
    all_lessons = []
    for module in course.modules.all().order_by('order'):
        for lesson_item in module.lessons.all().order_by('order'):
            all_lessons.append(lesson_item)
    
    current_index = None
    for i, lesson_item in enumerate(all_lessons):
        if lesson_item.id == lesson.id:
            current_index = i
            break
    
    prev_lesson = all_lessons[current_index - 1] if current_index and current_index > 0 else None
    next_lesson = all_lessons[current_index + 1] if current_index is not None and current_index < len(all_lessons) - 1 else None
    
    context = {
        'course': course,
        'lesson': lesson,
        'progress': progress,
        'prev_lesson': prev_lesson,
        'next_lesson': next_lesson,
    }
    
    return render(request, 'courses/lesson_view.html', context)

@login_required
@user_passes_test(is_student)
def mark_lesson_complete(request, course_id, lesson_id):
    """Marquer une leçon comme terminée"""
    if request.method == 'POST':
        course = get_object_or_404(Course, id=course_id, is_published=True)
        lesson = get_object_or_404(Lesson, id=lesson_id)
        student = request.user
        
        # Vérifier que l'étudiant est inscrit
        if not course.students.filter(id=student.id).exists():
            return JsonResponse({'success': False, 'error': 'Non inscrit au cours'})
        
        # Récupérer ou créer le progress
        progress, _ = Progress.objects.get_or_create(
            student=student,
            course=course,
            lesson=lesson,
            defaults={'completed': False}
        )
        
        # Basculer le statut
        progress.completed = not progress.completed
        if progress.completed:
            progress.completed_at = timezone.now()
        else:
            progress.completed_at = None
        progress.save()
        
        # Calculer la nouvelle progression
        new_progress_rate = course.get_completion_rate(student)
        
        return JsonResponse({
            'success': True,
            'completed': progress.completed,
            'progress_rate': new_progress_rate
        })
    
    return JsonResponse({'success': False, 'error': 'Méthode non autorisée'})

@login_required
@user_passes_test(is_student)
def download_file(request, course_id, lesson_id):
    """Télécharger un fichier de leçon"""
    course = get_object_or_404(Course, id=course_id, is_published=True)
    lesson = get_object_or_404(Lesson, id=lesson_id)
    student = request.user
    
    # Vérifier que l'étudiant est inscrit
    if not course.students.filter(id=student.id).exists():
        raise Http404("Accès non autorisé")
    
    # Vérifier que la leçon a un fichier
    if not lesson.file:
        raise Http404("Aucun fichier disponible")
    
    # Servir le fichier
    response = HttpResponse(lesson.file.read(), content_type='application/octet-stream')
    response['Content-Disposition'] = f'attachment; filename="{lesson.file.name.split("/")[-1]}"'
    return response
