{% extends 'base.html' %}
{% load static %}

{% block title %}{{ course.title }} - <PERSON><PERSON><PERSON> du Cours{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .course-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .course-hero {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    border-radius: 20px;
    padding: 3rem 2rem;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
    overflow: hidden;
  }

  .course-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }

  .course-hero-content {
    position: relative;
    z-index: 2;
  }

  .course-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .course-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
  }

  .course-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
  }

  .course-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .btn-enroll {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-enroll:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(67, 233, 123, 0.3);
    color: white;
    text-decoration: none;
  }

  .btn-enrolled {
    background: #28a745;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-login {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-login:hover {
    transform: translateY(-3px);
    color: white;
    text-decoration: none;
  }

  .course-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
  }

  .main-content {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .sidebar {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .course-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    margin-bottom: 2rem;
  }

  .modules-list {
    list-style: none;
    padding: 0;
  }

  .module-item {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #C8A8E9;
  }

  .module-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .module-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .lessons-count {
    color: #777;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .instructor-card {
    text-align: center;
    margin-bottom: 2rem;
  }

  .instructor-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
  }

  .instructor-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .instructor-title {
    color: #666;
    font-size: 0.9rem;
  }

  .course-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 15px;
  }

  .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #C8A8E9;
    display: block;
  }

  .stat-label {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
  }

  .requirements-list {
    list-style: none;
    padding: 0;
  }

  .requirement-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    color: #555;
  }

  .requirement-item i {
    color: #28a745;
  }

  @media (max-width: 768px) {
    .course-content {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .course-title {
      font-size: 2rem;
    }

    .course-meta {
      gap: 1rem;
    }

    .course-actions {
      flex-direction: column;
    }

    .sidebar {
      position: static;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="course-detail-container">
  <!-- Hero Section -->
  <div class="course-hero">
    <div class="course-hero-content">
      <h1 class="course-title">{{ course.title }}</h1>
      <p class="course-subtitle">{{ course.description|truncatewords:20 }}</p>
      
      <div class="course-meta">
        <div class="meta-item">
          <i class="fas fa-signal"></i>
          <span>{{ course.get_level_display }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-users"></i>
          <span>{{ course.students.count }} étudiants</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-book"></i>
          <span>{{ course.modules.count }} modules</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-tag"></i>
          <span>{{ course.get_category_display }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-euro-sign"></i>
          <span>
            {% if course.is_free or course.price == 0 %}
              Gratuit
            {% else %}
              {{ course.price }}€
            {% endif %}
          </span>
        </div>
      </div>

      <div class="course-actions">
        {% if user.is_authenticated %}
          {% if is_enrolled %}
            <a href="{% url 'course_learn' course.id %}" class="btn-enrolled">
              <i class="fas fa-play"></i>
              Continuer le cours
            </a>
          {% else %}
            {% if course.is_free or course.price == 0 %}
              <a href="{% url 'enroll_course' course.id %}" class="btn-enroll">
                <i class="fas fa-plus"></i>
                S'inscrire gratuitement
              </a>
            {% else %}
              <a href="{% url 'enroll_course' course.id %}" class="btn-enroll">
                <i class="fas fa-credit-card"></i>
                Acheter pour {{ course.price }}€
              </a>
            {% endif %}
          {% endif %}
        {% else %}
          <a href="{% url 'login' %}" class="btn-login">
            <i class="fas fa-sign-in-alt"></i>
            Se connecter pour s'inscrire
          </a>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="course-content">
    <div class="main-content">
      <div class="section-title">
        <i class="fas fa-info-circle"></i>
        Description du cours
      </div>
      <div class="course-description">
        {{ course.description|linebreaks }}
      </div>

      <div class="section-title">
        <i class="fas fa-list"></i>
        Contenu du cours
      </div>
      <ul class="modules-list">
        {% for module in course.modules.all %}
        <li class="module-item">
          <div class="module-title">
            <i class="fas fa-folder"></i>
            {{ module.title }}
          </div>
          <div class="module-description">
            {{ module.description|truncatewords:15 }}
          </div>
          <div class="lessons-count">
            <i class="fas fa-play-circle"></i>
            {{ module.lessons.count }} leçon{{ module.lessons.count|pluralize }}
          </div>
        </li>
        {% empty %}
        <li class="module-item">
          <div class="module-title">
            <i class="fas fa-info-circle"></i>
            Contenu en préparation
          </div>
          <div class="module-description">
            Le contenu de ce cours est actuellement en cours de préparation.
          </div>
        </li>
        {% endfor %}
      </ul>
    </div>

    <div class="sidebar">
      <!-- Instructeur -->
      <div class="instructor-card">
        <div class="instructor-avatar">
          {{ course.instructor.username|first|upper }}
        </div>
        <div class="instructor-name">{{ course.instructor.get_full_name|default:course.instructor.username }}</div>
        <div class="instructor-title">Instructeur</div>
      </div>

      <!-- Statistiques -->
      <div class="course-stats">
        <div class="stat-item">
          <span class="stat-number">{{ course.students.count }}</span>
          <div class="stat-label">Étudiants inscrits</div>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ total_lessons }}</span>
          <div class="stat-label">Leçons</div>
        </div>
      </div>

      <!-- Prérequis -->
      {% if course.prerequisites %}
      <div class="section-title">
        <i class="fas fa-check-circle"></i>
        Prérequis
      </div>
      <ul class="requirements-list">
        {% for req in course.prerequisites_list %}
        <li class="requirement-item">
          <i class="fas fa-check"></i>
          <span>{{ req }}</span>
        </li>
        {% endfor %}
      </ul>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
