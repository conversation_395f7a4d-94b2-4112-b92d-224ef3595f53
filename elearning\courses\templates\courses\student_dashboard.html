{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard Étudiant{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .student-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .dashboard-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-5px);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
  }

  .stat-icon.enrolled { background: linear-gradient(135deg, #667eea, #764ba2); }
  .stat-icon.completed { background: linear-gradient(135deg, #43e97b, #38f9d7); }
  .stat-icon.progress { background: linear-gradient(135deg, #f093fb, #f5576c); }

  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: #666;
    font-size: 0.9rem;
  }

  .action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .action-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
  }

  .action-card:hover {
    transform: translateY(-5px);
  }

  .action-btn {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #333;
    text-decoration: none;
  }

  .section-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .course-progress-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.3s ease;
  }

  .course-progress-item:hover {
    background-color: #f8f9fa;
  }

  .course-progress-item:last-child {
    border-bottom: none;
  }

  .course-image {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
  }

  .progress-bar-container {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
  }

  .progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  .progress-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
  }

  .recommended-course {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
  }

  .recommended-course:hover {
    transform: translateY(-5px);
  }

  .course-card-image {
    height: 150px;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
  }

  .course-card-content {
    padding: 1.5rem;
  }

  .course-card-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #333;
  }

  .course-card-meta {
    display: flex;
    justify-content: between;
    font-size: 0.8rem;
    color: #666;
    margin-top: 1rem;
  }

  .empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #C8A8E9;
  }
</style>
{% endblock %}

{% block content %}
<div class="student-dashboard">
  <div class="dashboard-header">
    <h1><i class="fas fa-user-graduate"></i> Dashboard Étudiant</h1>
    <p>Bienvenue {{ request.user.username }}, continuez votre apprentissage !</p>
  </div>

  <!-- Statistiques principales -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon enrolled">
        <i class="fas fa-book"></i>
      </div>
      <div class="stat-number">{{ total_enrolled }}</div>
      <div class="stat-label">Cours Inscrits</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon completed">
        <i class="fas fa-check-circle"></i>
      </div>
      <div class="stat-number">{{ completed_courses }}</div>
      <div class="stat-label">Cours Terminés</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon progress">
        <i class="fas fa-chart-line"></i>
      </div>
      <div class="stat-number">{{ avg_progress }}%</div>
      <div class="stat-label">Progression Moyenne</div>
    </div>
  </div>

  <!-- Actions rapides -->
  <div class="action-cards">
    <div class="action-card">
      <i class="fas fa-search" style="font-size: 3rem; color: #C8A8E9; margin-bottom: 1rem;"></i>
      <h4>Explorer les Cours</h4>
      <p class="text-muted mb-3">Découvrez de nouveaux cours dans notre catalogue</p>
      <a href="{% url 'course_catalog' %}" class="action-btn">
        <i class="fas fa-compass"></i>
        Catalogue
      </a>
    </div>

    <div class="action-card">
      <i class="fas fa-graduation-cap" style="font-size: 3rem; color: #A8D0F0; margin-bottom: 1rem;"></i>
      <h4>Mes Cours</h4>
      <p class="text-muted mb-3">Accédez à vos cours et continuez votre apprentissage</p>
      <a href="{% url 'student_my_courses' %}" class="action-btn">
        <i class="fas fa-play"></i>
        Continuer
      </a>
    </div>

    <div class="action-card">
      <i class="fas fa-envelope" style="font-size: 3rem; color: #C8A8E9; margin-bottom: 1rem;"></i>
      <h4>Messages</h4>
      <p class="text-muted mb-3">Communiquez avec vos instructeurs</p>
      <a href="#" class="action-btn">
        <i class="fas fa-comments"></i>
        Messagerie
      </a>
    </div>
  </div>

  <div class="row">
    <!-- Progression des cours -->
    <div class="col-md-8">
      <div class="section-card">
        <h3><i class="fas fa-chart-line"></i> Ma Progression</h3>
        {% if course_progress %}
          {% for item in course_progress %}
          <div class="course-progress-item">
            <div class="course-image">
              {% if item.course.image %}
                <img src="{{ item.course.image.url }}" alt="{{ item.course.title }}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 10px;">
              {% else %}
                <i class="fas fa-graduation-cap"></i>
              {% endif %}
            </div>
            <div style="flex: 1;">
              <div class="d-flex justify-content-between align-items-start">
                <div>
                  <strong>{{ item.course.title }}</strong><br>
                  <small class="text-muted">{{ item.completed_lessons }} leçons complétées</small>
                </div>
                <a href="{% url 'course_learn' item.course.id %}" class="btn btn-sm btn-outline-primary">
                  Continuer
                </a>
              </div>
              <div class="progress-bar-container">
                <div class="progress-bar" style="width: {{ item.progress }}%"></div>
              </div>
              <div class="progress-text">{{ item.progress }}% complété</div>
            </div>
          </div>
          {% endfor %}
        {% else %}
        <div class="empty-state">
          <div class="empty-icon">
            <i class="fas fa-book-open"></i>
          </div>
          <p>Aucun cours inscrit</p>
          <a href="{% url 'course_catalog' %}" class="action-btn">
            <i class="fas fa-plus"></i> Explorer les Cours
          </a>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- Cours recommandés -->
    <div class="col-md-4">
      <div class="section-card">
        <h3><i class="fas fa-star"></i> Cours Recommandés</h3>
        {% for course in recommended_courses %}
        <div class="recommended-course mb-3">
          <div class="course-card-image">
            {% if course.image %}
              <img src="{{ course.image.url }}" alt="{{ course.title }}" style="width: 100%; height: 100%; object-fit: cover;">
            {% else %}
              <i class="fas fa-graduation-cap"></i>
            {% endif %}
          </div>
          <div class="course-card-content">
            <div class="course-card-title">{{ course.title }}</div>
            <p class="text-muted" style="font-size: 0.8rem;">{{ course.description|truncatewords:10 }}</p>
            <div class="course-card-meta">
              <span><i class="fas fa-users"></i> {{ course.student_count }}</span>
              <span><i class="fas fa-tag"></i> {{ course.get_category_display }}</span>
            </div>
            <a href="{% url 'course_detail_public' course.id %}" class="btn btn-sm btn-outline-primary mt-2">
              Voir le cours
            </a>
          </div>
        </div>
        {% empty %}
        <p class="text-muted">Aucun cours recommandé pour le moment</p>
        {% endfor %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
