{% extends 'base.html' %}
{% block title %}Modules - {{ course.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
  <h2>📂 Modules du cours : {{ course.title }}</h2>
  {% for module in modules %}
    <div class="card mt-3">
      <div class="card-body">
        <h5 class="card-title">{{ module.title }}</h5>
        <ul class="list-group">
          {% for lesson in module.lesson_set.all %}
            <li class="list-group-item d-flex justify-content-between align-items-center">
              {{ lesson.title }}
              <a href="{% url 'lesson_detail' lesson.id %}" class="btn btn-sm btn-outline-primary">Voir</a>
            </li>
          {% empty %}
            <li class="list-group-item">Aucune leçon.</li>
          {% endfor %}
        </ul>
      </div>
    </div>
  {% empty %}
    <p>Aucun module disponible.</p>
  {% endfor %}
</div>
{% endblock %}
