from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from courses.models import Course
from courses.forms import CourseForm

@login_required
def add_course(request):
    """Vue pour ajouter un nouveau cours."""
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES)
        if form.is_valid():
            course = form.save(commit=False)
            # Si l'instructeur n'est pas défini, utiliser l'utilisateur actuel
            if not course.instructor:
                course.instructor = request.user
            course.save()
            messages.success(request, "Le cours a été créé avec succès.")
            return redirect('course_list')
    else:
        form = CourseForm(initial={'instructor': request.user})
    
    return render(request, 'courses/course_form.html', {
        'form': form, 
        'action': 'Ajouter'
    })