{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard Instructeur{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .instructor-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .dashboard-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-5px);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
  }

  .stat-icon.courses { background: linear-gradient(135deg, #667eea, #764ba2); }
  .stat-icon.students { background: linear-gradient(135deg, #f093fb, #f5576c); }
  .stat-icon.lessons { background: linear-gradient(135deg, #4facfe, #00f2fe); }
  .stat-icon.published { background: linear-gradient(135deg, #43e97b, #38f9d7); }

  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: #666;
    font-size: 0.9rem;
  }

  .action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .action-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
  }

  .action-card:hover {
    transform: translateY(-5px);
  }

  .action-btn {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #333;
    text-decoration: none;
  }

  .recent-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .course-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s ease;
  }

  .course-item:hover {
    background-color: #f8f9fa;
  }

  .course-item:last-child {
    border-bottom: none;
  }

  .badge-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
  }

  .badge-published {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
  }

  .badge-draft {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
    color: white;
  }
</style>
{% endblock %}

{% block content %}
<div class="instructor-dashboard">
  <div class="dashboard-header">
    <h1><i class="fas fa-chalkboard-teacher"></i> Dashboard Instructeur</h1>
    <p>Bienvenue {{ request.user.username }}, gérez vos cours et suivez vos étudiants</p>
  </div>

  <!-- Statistiques principales -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon courses">
        <i class="fas fa-book"></i>
      </div>
      <div class="stat-number">{{ total_courses }}</div>
      <div class="stat-label">Mes Cours</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon published">
        <i class="fas fa-check-circle"></i>
      </div>
      <div class="stat-number">{{ published_courses }}</div>
      <div class="stat-label">Cours Publiés</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon students">
        <i class="fas fa-user-graduate"></i>
      </div>
      <div class="stat-number">{{ total_students }}</div>
      <div class="stat-label">Étudiants Total</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon lessons">
        <i class="fas fa-play-circle"></i>
      </div>
      <div class="stat-number">{{ total_lessons }}</div>
      <div class="stat-label">Leçons Créées</div>
    </div>
  </div>

  <!-- Actions rapides -->
  <div class="action-cards">
    <div class="action-card">
      <i class="fas fa-plus-circle" style="font-size: 3rem; color: #C8A8E9; margin-bottom: 1rem;"></i>
      <h4>Créer un Nouveau Cours</h4>
      <p class="text-muted mb-3">Commencez à créer un nouveau cours pour vos étudiants</p>
      <a href="{% url 'instructor_create_course' %}" class="action-btn">
        <i class="fas fa-plus"></i>
        Créer un Cours
      </a>
    </div>

    <div class="action-card">
      <i class="fas fa-list-alt" style="font-size: 3rem; color: #A8D0F0; margin-bottom: 1rem;"></i>
      <h4>Gérer Mes Cours</h4>
      <p class="text-muted mb-3">Consultez et modifiez vos cours existants</p>
      <a href="{% url 'instructor_my_courses' %}" class="action-btn">
        <i class="fas fa-cog"></i>
        Mes Cours
      </a>
    </div>

    <div class="action-card">
      <i class="fas fa-envelope" style="font-size: 3rem; color: #C8A8E9; margin-bottom: 1rem;"></i>
      <h4>Messages</h4>
      <p class="text-muted mb-3">Communiquez avec vos étudiants</p>
      <a href="#" class="action-btn">
        <i class="fas fa-comments"></i>
        Messagerie
      </a>
    </div>
  </div>

  <div class="row">
    <!-- Cours récents -->
    <div class="col-md-6">
      <div class="recent-section">
        <h3><i class="fas fa-clock"></i> Cours Récents</h3>
        {% for course in recent_courses %}
        <div class="course-item">
          <div>
            <strong>{{ course.title }}</strong><br>
            <small class="text-muted">{{ course.created_at|date:"d/m/Y" }}</small>
          </div>
          <div>
            <span class="badge-status {% if course.is_published %}badge-published{% else %}badge-draft{% endif %}">
              {% if course.is_published %}Publié{% else %}Brouillon{% endif %}
            </span>
          </div>
        </div>
        {% empty %}
        <p class="text-muted">Aucun cours créé</p>
        {% endfor %}
      </div>
    </div>

    <!-- Messages récents -->
    <div class="col-md-6">
      <div class="recent-section">
        <h3><i class="fas fa-inbox"></i> Messages Récents</h3>
        {% for message in recent_messages %}
        <div class="course-item">
          <div>
            <strong>{{ message.subject }}</strong><br>
            <small class="text-muted">De: {{ message.sender.username }}</small>
          </div>
          <div>
            <small class="text-muted">{{ message.created_at|date:"d/m H:i" }}</small>
          </div>
        </div>
        {% empty %}
        <p class="text-muted">Aucun message</p>
        {% endfor %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
            <a href="#" class="list-group-item list-group-item-action">
              <i class="fas fa-chart-line me-2"></i> Statistiques
            </a>
            <a href="#" class="list-group-item list-group-item-action">
              <i class="fas fa-money-bill-wave me-2"></i> Revenus
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="col-lg-9">
      <!-- Stats Cards -->
      <div class="row g-4 mb-4">
        <div class="col-md-4">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-primary text-white me-3">
                  <i class="fas fa-book"></i>
                </div>
                <div>
                  <h6 class="card-subtitle text-muted">Total des cours</h6>
                  <h2 class="card-title mb-0">{{ courses|length|default:"0" }}</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-success text-white me-3">
                  <i class="fas fa-users"></i>
                </div>
                <div>
                  <h6 class="card-subtitle text-muted">Total des étudiants</h6>
                  <h2 class="card-title mb-0">125</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-warning text-white me-3">
                  <i class="fas fa-star"></i>
                </div>
                <div>
                  <h6 class="card-subtitle text-muted">Note moyenne</h6>
                  <h2 class="card-title mb-0">4.8</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Courses Table -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white py-3">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Mes cours</h5>
            <a href="{% url 'create_course' %}" class="btn btn-primary">
              <i class="fas fa-plus me-2"></i>Nouveau cours
            </a>
          </div>
        </div>
        <div class="card-body">
          {% if courses %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Titre</th>
                    <th>Catégorie</th>
                    <th>Étudiants</th>
                    <th>Statut</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for course in courses %}
                  <tr>
                    <td>
                      <div class="d-flex align-items-center">
                        <img src="{{ course.image.url|default:'https://via.placeholder.com/40' }}" alt="{{ course.title }}" class="rounded me-2" width="40" height="40">
                        <div>
                          <h6 class="mb-0">{{ course.title }}</h6>
                          <small class="text-muted">Créé le {{ course.created_at|date:"d/m/Y" }}</small>
                        </div>
                      </div>
                    </td>
                    <td>{{ course.get_category_display }}</td>
                    <td>{{ course.students.count }}</td>
                    <td>
                      {% if course.is_published %}
                        <span class="badge bg-success">Publié</span>
                      {% else %}
                        <span class="badge bg-secondary">Brouillon</span>
                      {% endif %}
                    </td>
                    <td>
                      <div class="btn-group">
                        <a href="{% url 'update_course' course.id %}" class="btn btn-sm btn-outline-primary">
                          <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'module_list' course.id %}" class="btn btn-sm btn-outline-info">
                          <i class="fas fa-list"></i>
                        </a>
                        <a href="{% url 'delete_course' course.id %}" class="btn btn-sm btn-outline-danger">
                          <i class="fas fa-trash"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <img src="{% static 'img/empty-courses.svg' %}" alt="Aucun cours" class="mb-3" width="150">
              <h5>Vous n'avez pas encore créé de cours</h5>
              <p class="text-muted">Commencez à créer votre premier cours dès maintenant</p>
              <a href="{% url 'create_course' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Créer un cours
              </a>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
  }
</style>
{% endblock %}
