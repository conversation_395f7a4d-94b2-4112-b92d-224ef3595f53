{% extends 'base.html' %}

{% block title %}Tableau de bord Instructeur - E-Learn+{% endblock %}

{% block content %}
<div class="container-fluid py-5">
  <div class="row">
    <!-- Sidebar -->
    <div class="col-lg-3">
      <div class="card shadow-sm border-0 mb-4">
        <div class="card-body p-4">
          <div class="text-center mb-4">
            <img src="{{ user.profile.avatar.url|default:'https://via.placeholder.com/100' }}" alt="{{ user.username }}" class="rounded-circle mb-3" width="100" height="100">
            <h4 class="mb-0">{{ user.get_full_name|default:user.username }}</h4>
            <p class="text-muted">Instructeur</p>
          </div>
          
          <div class="list-group list-group-flush">
            <a href="#" class="list-group-item list-group-item-action active">
              <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
            </a>
            <a href="#" class="list-group-item list-group-item-action">
              <i class="fas fa-book me-2"></i> Mes cours
            </a>
            <a href="#" class="list-group-item list-group-item-action">
              <i class="fas fa-users me-2"></i> Étudiants
            </a>
            <a href="#" class="list-group-item list-group-item-action">
              <i class="fas fa-comment me-2"></i> Commentaires
            </a>
            <a href="#" class="list-group-item list-group-item-action">
              <i class="fas fa-chart-line me-2"></i> Statistiques
            </a>
            <a href="#" class="list-group-item list-group-item-action">
              <i class="fas fa-money-bill-wave me-2"></i> Revenus
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="col-lg-9">
      <!-- Stats Cards -->
      <div class="row g-4 mb-4">
        <div class="col-md-4">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-primary text-white me-3">
                  <i class="fas fa-book"></i>
                </div>
                <div>
                  <h6 class="card-subtitle text-muted">Total des cours</h6>
                  <h2 class="card-title mb-0">{{ courses|length|default:"0" }}</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-success text-white me-3">
                  <i class="fas fa-users"></i>
                </div>
                <div>
                  <h6 class="card-subtitle text-muted">Total des étudiants</h6>
                  <h2 class="card-title mb-0">125</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-warning text-white me-3">
                  <i class="fas fa-star"></i>
                </div>
                <div>
                  <h6 class="card-subtitle text-muted">Note moyenne</h6>
                  <h2 class="card-title mb-0">4.8</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Courses Table -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white py-3">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Mes cours</h5>
            <a href="{% url 'create_course' %}" class="btn btn-primary">
              <i class="fas fa-plus me-2"></i>Nouveau cours
            </a>
          </div>
        </div>
        <div class="card-body">
          {% if courses %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Titre</th>
                    <th>Catégorie</th>
                    <th>Étudiants</th>
                    <th>Statut</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for course in courses %}
                  <tr>
                    <td>
                      <div class="d-flex align-items-center">
                        <img src="{{ course.image.url|default:'https://via.placeholder.com/40' }}" alt="{{ course.title }}" class="rounded me-2" width="40" height="40">
                        <div>
                          <h6 class="mb-0">{{ course.title }}</h6>
                          <small class="text-muted">Créé le {{ course.created_at|date:"d/m/Y" }}</small>
                        </div>
                      </div>
                    </td>
                    <td>{{ course.get_category_display }}</td>
                    <td>{{ course.students.count }}</td>
                    <td>
                      {% if course.is_published %}
                        <span class="badge bg-success">Publié</span>
                      {% else %}
                        <span class="badge bg-secondary">Brouillon</span>
                      {% endif %}
                    </td>
                    <td>
                      <div class="btn-group">
                        <a href="{% url 'update_course' course.id %}" class="btn btn-sm btn-outline-primary">
                          <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'module_list' course.id %}" class="btn btn-sm btn-outline-info">
                          <i class="fas fa-list"></i>
                        </a>
                        <a href="{% url 'delete_course' course.id %}" class="btn btn-sm btn-outline-danger">
                          <i class="fas fa-trash"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <img src="{% static 'img/empty-courses.svg' %}" alt="Aucun cours" class="mb-3" width="150">
              <h5>Vous n'avez pas encore créé de cours</h5>
              <p class="text-muted">Commencez à créer votre premier cours dès maintenant</p>
              <a href="{% url 'create_course' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Créer un cours
              </a>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
  }
</style>
{% endblock %}
