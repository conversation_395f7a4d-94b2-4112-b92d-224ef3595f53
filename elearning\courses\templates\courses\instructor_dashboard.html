{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard Instructeur{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .instructor-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .dashboard-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .dashboard-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
  }

  .dashboard-header p {
    margin: 0.5rem 0 0 0;
    font-size: 1.1rem;
    opacity: 0.8;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-5px);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
  }

  .stat-icon.courses {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stat-icon.students {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-icon.lessons {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .stat-icon.published {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
  }

  .stat-content p {
    margin: 0;
    color: #718096;
    font-weight: 500;
  }

  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    text-decoration: none;
    color: #2d3748;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .action-btn:hover {
    border-color: #C8A8E9;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    color: #2d3748;
  }

  .action-btn i {
    font-size: 1.25rem;
    color: #667eea;
  }

  .content-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .content-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
  }

  .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-header h4 {
    margin: 0;
    color: #2d3748;
    font-weight: 600;
  }

  .card-body {
    padding: 1.5rem;
  }

  .course-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .course-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    transition: all 0.3s ease;
  }

  .course-item:hover {
    border-color: #C8A8E9;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }

  .course-info h5 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
  }

  .course-info p {
    margin: 0 0 0.5rem 0;
    color: #718096;
  }

  .course-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
  }

  .badge-published {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
  }

  .badge-draft {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
  }

  .course-actions {
    display: flex;
    gap: 0.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .btn-outline-primary {
    border: 1px solid #667eea;
    color: #667eea;
    background: transparent;
  }

  .btn-outline-secondary {
    border: 1px solid #718096;
    color: #718096;
    background: transparent;
  }

  .btn:hover {
    transform: translateY(-2px);
  }

  .empty-state {
    text-align: center;
    padding: 2rem;
    color: #718096;
  }

  .empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  @media (max-width: 768px) {
    .content-section {
      grid-template-columns: 1fr;
    }
    
    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .course-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="instructor-dashboard">
  <div class="dashboard-header">
    <h1><i class="fas fa-chalkboard-teacher"></i> Dashboard Instructeur</h1>
    <p>Bienvenue {{ request.user.username }}, gérez vos cours et suivez vos étudiants</p>
  </div>

  <!-- Actions rapides -->
  <div class="quick-actions">
    <a href="{% url 'instructor_create_course' %}" class="action-btn">
      <i class="fas fa-plus"></i>
      Créer un cours
    </a>
    <a href="{% url 'instructor_my_courses' %}" class="action-btn">
      <i class="fas fa-book"></i>
      Mes cours
    </a>
    <a href="{% url 'quizzes:instructor_quiz_list' %}" class="action-btn">
      <i class="fas fa-question-circle"></i>
      Mes quiz
    </a>
    <a href="#" class="action-btn">
      <i class="fas fa-chart-bar"></i>
      Statistiques
    </a>
    <a href="#" class="action-btn">
      <i class="fas fa-envelope"></i>
      Messages
    </a>
  </div>

  <!-- Statistiques -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon courses">
        <i class="fas fa-book"></i>
      </div>
      <div class="stat-content">
        <h3>{{ total_courses }}</h3>
        <p>Mes Cours</p>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon students">
        <i class="fas fa-users"></i>
      </div>
      <div class="stat-content">
        <h3>{{ total_students }}</h3>
        <p>Étudiants</p>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon lessons">
        <i class="fas fa-play-circle"></i>
      </div>
      <div class="stat-content">
        <h3>{{ total_lessons }}</h3>
        <p>Leçons</p>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon published">
        <i class="fas fa-globe"></i>
      </div>
      <div class="stat-content">
        <h3>{{ published_courses }}</h3>
        <p>Publiés</p>
      </div>
    </div>
  </div>

  <!-- Contenu principal -->
  <div class="content-section">
    <div class="content-card">
      <div class="card-header">
        <h4><i class="fas fa-book me-2"></i>Mes Cours Récents</h4>
        <a href="{% url 'instructor_my_courses' %}" class="btn btn-outline-primary btn-sm">
          Voir tous
        </a>
      </div>
      <div class="card-body">
        {% if recent_courses %}
          <div class="course-list">
            {% for course in recent_courses %}
            <div class="course-item">
              <div class="course-info">
                <h5>{{ course.title }}</h5>
                <p class="text-muted">{{ course.description|truncatewords:10 }}</p>
                <div class="course-meta">
                  <span class="badge badge-{{ course.is_published|yesno:'published,draft' }}">
                    {{ course.is_published|yesno:'Publié,Brouillon' }}
                  </span>
                  <small class="text-muted">{{ course.students.count }} étudiants</small>
                </div>
              </div>
              <div class="course-actions">
                <a href="{% url 'instructor_course_detail' course.id %}" class="btn btn-sm btn-outline-primary" title="Voir le cours">
                  <i class="fas fa-eye"></i>
                </a>
                <a href="{% url 'instructor_edit_course' course.id %}" class="btn btn-sm btn-outline-secondary" title="Modifier le cours">
                  <i class="fas fa-edit"></i>
                </a>
                <a href="{% url 'quizzes:create_quiz' course.id %}" class="btn btn-sm btn-outline-success" title="Créer un quiz">
                  <i class="fas fa-question-circle"></i>
                </a>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="empty-state">
            <i class="fas fa-book-open"></i>
            <h5>Aucun cours créé</h5>
            <p>Commencez par créer votre premier cours</p>
            <a href="{% url 'instructor_create_course' %}" class="btn btn-primary">
              <i class="fas fa-plus"></i> Créer un cours
            </a>
          </div>
        {% endif %}
      </div>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h4><i class="fas fa-envelope me-2"></i>Messages Récents</h4>
      </div>
      <div class="card-body">
        {% if recent_messages %}
          <div class="message-list">
            {% for message in recent_messages %}
            <div class="message-item">
              <div class="message-avatar">
                {{ message.sender.username|first|upper }}
              </div>
              <div class="message-content">
                <strong>{{ message.sender.username }}</strong>
                <p>{{ message.content|truncatewords:8 }}</p>
                <small class="text-muted">{{ message.created_at|timesince }} ago</small>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="empty-state">
            <i class="fas fa-inbox"></i>
            <p>Aucun message récent</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
