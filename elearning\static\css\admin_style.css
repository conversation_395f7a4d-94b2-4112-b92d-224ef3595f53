:root {
  --primary: #4361ee;
  --secondary: #3a0ca3;
  --accent: #f72585;
  --success: #4cc9f0;
  --warning: #f8961e;
  --danger: #f94144;
  --light: #f8f9fa;
  --dark: #212529;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-800: #343a40;
  --border-radius: 0.5rem;
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
}

/* Admin Dashboard Styles */
.admin-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Styles pour l'en-tête du dashboard admin */
.admin-header {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.admin-header h1 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.admin-header p {
  margin-bottom: 0;
  opacity: 0.9;
}

/* Form Styles */
.admin-form {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 2rem;
}

.admin-form .form-control, 
.admin-form .form-select {
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  transition: var(--transition);
}

.admin-form .form-control:focus,
.admin-form .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.admin-form .input-group-text {
  background-color: white;
  border-right: none;
}

/* Boutons */
.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: var(--transition);
}

.btn-primary:hover {
  background-color: var(--secondary);
  border-color: var(--secondary);
  transform: translateY(-2px);
}

.btn-danger {
  background-color: var(--danger);
  border-color: var(--danger);
  color: white;
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: var(--transition);
}

.btn-danger:hover {
  background-color: #e71d36;
  border-color: #e71d36;
  transform: translateY(-2px);
}

.btn-accent {
  background-color: var(--accent);
  border-color: var(--accent);
  color: white;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: var(--transition);
}

.btn-accent:hover {
  background-color: #d61c69;
  border-color: #d61c69;
  transform: translateY(-2px);
}

/* Card styles */
.card {
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-bottom: 2rem;
}

.card-header {
  padding: 1rem 1.5rem;
  font-weight: 500;
}

.bg-primary {
  background-color: var(--primary) !important;
}

/* Badge styles */
.badge {
  font-weight: 500;
  letter-spacing: 0.5px;
}

.badge.rounded-pill {
  padding: 0.5rem 1rem;
}

/* Pagination */
.pagination .page-link {
  color: var(--primary);
  border-radius: 0.25rem;
  margin: 0 0.2rem;
  transition: var(--transition);
}

.pagination .page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
}

.pagination .page-link:hover {
  background-color: var(--gray-100);
  transform: translateY(-2px);
}

/* Modal styles */
.modal-content {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.modal-header {
  padding: 1rem 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-stats {
    grid-template-columns: 1fr;
  }
  
  .admin-actions {
    grid-template-columns: 1fr;
  }
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  border-left: 5px solid var(--primary);
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card.students {
  border-left-color: var(--success);
}

.stat-card.courses {
  border-left-color: var(--primary);
}

.stat-card.instructors {
  border-left-color: var(--accent);
}

.stat-card.modules {
  border-left-color: var(--warning);
}

.stat-card h3 {
  font-size: 1.1rem;
  color: var(--gray-800);
  margin-bottom: 0.5rem;
}

.stat-card .number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark);
}

.stat-card .icon {
  font-size: 2rem;
  opacity: 0.2;
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.admin-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.action-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2);
}

.action-card h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: var(--primary);
  border-bottom: 2px solid var(--gray-200);
  padding-bottom: 0.5rem;
}

.action-card .btn-admin {
  margin-top: auto;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  background-color: var(--primary);
  color: white;
  text-decoration: none;
  text-align: center;
  font-weight: 500;
  transition: var(--transition);
  border: none;
  cursor: pointer;
}

.action-card .btn-admin:hover {
  background-color: var(--secondary);
  transform: translateY(-2px);
}

.action-card .btn-admin.courses {
  background-color: var(--primary);
}

.action-card .btn-admin.users {
  background-color: var(--accent);
}

.action-card .btn-admin.instructors {
  background-color: var(--warning);
}

.action-card .btn-admin.modules {
  background-color: var(--success);
}

/* Table Styles */
.admin-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  margin-bottom: 2rem;
}

.admin-table thead th {
  background-color: var(--primary);
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

.admin-table tbody tr {
  background-color: white;
  transition: var(--transition);
}

.admin-table tbody tr:hover {
  background-color: var(--gray-100);
}

.admin-table tbody td {
  padding: 1rem;
  border-top: 1px solid var(--gray-200);
}

.admin-table .badge {
  padding: 0.5rem 0.75rem;
  border-radius: 50px;
  font-weight: 500;
  font-size: 0.75rem;
}

/* Form Styles */
.admin-form {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 2rem;
}

.admin-form h2 {
  margin-bottom: 1.5rem;
  color: var(--primary);
  border-bottom: 2px solid var(--gray-200);
  padding-bottom: 0.5rem;
}

.admin-form .form-control {
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  transition: var(--transition);
}

.admin-form .form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.admin-form .btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.admin-form .btn-primary:hover {
  background-color: var(--secondary);
  border-color: var(--secondary);
  transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-stats {
    grid-template-columns: 1fr;
  }
  
  .admin-actions {
    grid-template-columns: 1fr;
  }
}


