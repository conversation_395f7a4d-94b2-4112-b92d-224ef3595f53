{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Créer un cours{% endblock %}

{% block content %}
<div class="container py-4">
  <h2 class="mb-4 text-center">➕ Créer un nouveau cours</h2>
  <form method="POST" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="card shadow-sm mb-4">
      <div class="card-header bg-primary text-white">📝 Détails du cours</div>
      <div class="card-body">
        {{ course_form|crispy }}
      </div>
    </div>

    <div class="card shadow-sm mb-4">
      <div class="card-header bg-secondary text-white">📦 Modules du cours</div>
      <div class="card-body">
        {{ module_formset.management_form }}
        {% for form in module_formset %}
          <div class="border p-3 mb-3 bg-light rounded">
            {{ form|crispy }}
          </div>
        {% endfor %}
        <small class="text-muted">Ajoutez au moins un module pour commencer.</small>
      </div>
    </div>

    <div class="text-center">
      <button type="submit" class="btn btn-success btn-lg px-4"> Enregistrer le cours</button>
    </div>
  </form>
</div>
{% endblock %}
