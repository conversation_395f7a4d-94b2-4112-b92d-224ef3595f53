from django.db import models
from django.conf import settings
from courses.models import Course

class Message(models.Model):
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='sent_messages')
    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='received_messages')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='messages', blank=True, null=True)
    subject = models.CharField(max_length=200)
    content = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"De {self.sender.username} à {self.recipient.username}: {self.subject}"

class Notification(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(max_length=200)
    message = models.TextField()
    is_read = models.<PERSON><PERSON>anField(default=False)
    notification_type = models.CharField(max_length=50, choices=[
        ('course_enrollment', 'Inscription au cours'),
        ('new_lesson', 'Nouvelle leçon'),
        ('message', 'Nouveau message'),
        ('progress', 'Progression'),
        ('system', 'Système')
    ], default='system')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.username}: {self.title}"
