{% extends 'base.html' %}
{% load static %}

{% block title %}Modifier un cours{% endblock %}

{% block extra_css %}
<style>
  /* Supprimer le titre dupliqué */
  .form-header h1, .form-header h2, .form-header .form-title {
    display: none;
  }
  
  /* Style moderne pour le formulaire */
  form {
    background: white;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    padding: 0;
    margin: 30px 0;
  }
  
  /* En-tête du formulaire avec dégradé */
  .form-header, .card-header, .header-banner {
    background: linear-gradient(135deg, #5e60ce, #6930c3, #7400b8);
    color: white;
    padding: 25px 30px;
    position: relative;
    overflow: hidden;
  }
  
  /* Motif décoratif pour l'en-tête */
  .form-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.3;
  }
  
  /* Corps du formulaire */
  .form-body {
    padding: 30px;
  }
  
  /* Style amélioré pour les champs de formulaire */
  .form-group {
    margin-bottom: 25px;
    position: relative;
  }
  
  /* Labels flottants et stylisés */
  .form-label {
    position: absolute;
    left: 15px;
    top: -10px;
    background: white;
    padding: 0 10px;
    font-size: 0.85rem;
    font-weight: 700;
    color: #6930c3;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    z-index: 1;
    transition: all 0.3s;
  }
  
  /* Champs avec bordure et ombre */
  .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 1rem;
    transition: all 0.3s;
    background: #f8f9fa;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.03);
  }
  
  /* Effet au survol */
  .form-control:hover {
    border-color: #ced4da;
    background: #fff;
  }
  
  /* Effet au focus */
  .form-control:focus {
    border-color: #6930c3;
    box-shadow: 0 0 0 3px rgba(105, 48, 195, 0.2);
    background: #fff;
  }
  
  /* Style pour le champ titre */
  #id_title {
    font-weight: 600;
    font-size: 1.1rem;
    border-left: 4px solid #6930c3;
  }
  
  /* Style pour la description */
  #id_description {
    min-height: 120px;
    line-height: 1.6;
    border-left: 4px solid #6930c3;
  }
  
  /* Style pour les sélecteurs */
  select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236930c3' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
  }
  
  /* Style pour le champ durée */
  #id_duration {
    border-left: 4px solid #6930c3;
  }
  
  /* Style pour le sélecteur de fichier */
  .file-upload {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px dashed #ced4da;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
  }
  
  .file-upload:hover {
    border-color: #6930c3;
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
  }
  
  .file-upload-label {
    display: inline-block;
    background: #6930c3;
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 10px;
    transition: all 0.3s;
  }
  
  .file-upload:hover .file-upload-label {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(105, 48, 195, 0.3);
  }
  
  /* Checkbox stylisée */
  input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #6c63ff;
    margin-right: 10px;
  }
  
  /* Boutons d'action */
  button, .btn, input[type="submit"] {
    padding: 14px 30px;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s;
    cursor: pointer;
  }
  
  /* Bouton principal */
  #enregistrer, .btn-primary, input[type="submit"] {
    background: linear-gradient(135deg, #5e60ce, #6930c3);
    color: white;
    box-shadow: 0 5px 15px rgba(105, 48, 195, 0.3);
  }
  
  #enregistrer:hover, .btn-primary:hover, input[type="submit"]:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(105, 48, 195, 0.4);
  }
  
  /* Bouton secondaire */
  #annuler, .btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
  }
  
  #annuler:hover, .btn-secondary:hover {
    transform: translateY(-3px);
    background: #e9ecef;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  /* Conteneur de boutons */
  .button-container {
    display: flex;
    gap: 15px;
    margin-top: 30px;
  }
  
  /* Grille pour les champs */
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
    
    .button-container {
      flex-direction: column;
    }
  }
  
  /* Animation de pulsation pour les boutons */
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(105, 48, 195, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(105, 48, 195, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(105, 48, 195, 0);
    }
  }
  
  #enregistrer {
    animation: pulse 2s infinite;
  }
  
  /* Effet de flottement pour les champs */
  input:focus, textarea:focus, select:focus {
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes float {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
    100% {
      transform: translateY(0);
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="form-header">
  <h1>Modifier un cours</h1>
</div>

<div class="form-body">
  <form method="post" enctype="multipart/form-data">
    {% csrf_token %}
    
    <div class="form-group">
      <label for="id_title" class="form-label">Titre</label>
      <input type="text" name="title" id="id_title" class="form-control" value="{{ form.title.value|default:'' }}">
      <div class="form-progress"></div>
    </div>
    
    <div class="form-group">
      <label for="id_description" class="form-label">Description</label>
      <textarea name="description" id="id_description" class="form-control">{{ form.description.value|default:'' }}</textarea>
      <div class="form-progress"></div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label for="id_category" class="form-label">Catégorie</label>
          {{ form.category }}
          <div class="form-progress"></div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <label for="id_level" class="form-label">Niveau</label>
          {{ form.level }}
          <div class="form-progress"></div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label for="id_instructor" class="form-label">Instructeur</label>
          {{ form.instructor }}
          <div class="form-progress"></div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <label for="id_duration" class="form-label">Durée (heures)</label>
          <input type="text" name="duration" id="id_duration" class="form-control" value="{{ form.duration.value|default:'' }}">
          <div class="form-progress"></div>
        </div>
      </div>
    </div>
    
    <div class="form-group">
      <label class="form-label">Image du cours</label>
      <div class="file-upload">
        <span class="file-upload-label">Choisir un fichier</span>
        {{ form.image }}
        <p class="file-upload-info">Format recommandé: JPG, PNG. Taille max: 5MB</p>
      </div>
      {% if form.instance.image %}
        <div class="mt-2">
          <img src="{{ form.instance.image.url }}" alt="Image actuelle" style="max-width: 200px; border-radius: 8px;">
        </div>
      {% endif %}
    </div>
    
    <div class="form-check">
      <input type="checkbox" name="is_published" id="id_is_published" class="form-check-input" {% if form.is_published.value %}checked{% endif %}>
      <label for="id_is_published" class="form-check-label">Publier le cours</label>
    </div>
    
    <div class="button-container">
      <button type="submit" class="btn-save">Enregistrer</button>
      <a href="{% url 'course_detail' form.instance.id %}" class="btn-cancel">Annuler</a>
    </div>
  </form>
</div>
{% endblock %}




