{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}
{% block content %}
<div class="container d-flex justify-content-center align-items-center" style="min-height: 100vh;">
  <div class="card shadow-lg p-4" style="width: 100%; max-width: 400px;">
    <h3 class="text-center mb-4">Login</h3>
    {% if error %}
      <div class="alert alert-danger" role="alert">
        {{ error }}
      </div>
    {% endif %}
    <form method="post" novalidate>
      {% csrf_token %}
      {{ form|crispy }}
      <div class="d-grid">
        <button type="submit" class="btn btn-primary">Login</button>
      </div>
    </form>
    <p class="text-center mt-3">
      Don't have an account? <a href="{% url 'register' %}">Register here</a>
    </p>
  </div>
</div>
{% endblock %}