{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard Admin{% endblock %}

{% block content %}
<div class="container py-5">
  <h2 class="mb-4 text-center">🎛️ Tableau de bord Administrateur</h2>

  <!-- Statistiques Résumées -->
  <div class="row text-center mb-4">
    <div class="col-md-4">
      <div class="card shadow-sm border-primary">
        <div class="card-body">
          <h5 class="card-title">👨‍🏫 Instructeurs</h5>
          <p class="display-6 text-primary">{{ instructors.count }}</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm border-success">
        <div class="card-body">
          <h5 class="card-title">👨‍🎓 Étudiants</h5>
          <p class="display-6 text-success">{{ students.count }}</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm border-info">
        <div class="card-body">
          <h5 class="card-title">📘 Cours</h5>
          <p class="display-6 text-info">{{ courses.count }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Liste des Cours -->
  <h4 class="mt-5">📚 Tous les cours</h4>
  <table class="table table-hover table-bordered mt-3">
    <thead class="table-light">
      <tr>
        <th>Titre</th>
        <th>Catégorie</th>
        <th>Instructeur</th>
        <th>Étudiants</th>
        <th>Statut</th>
      </tr>
    </thead>
    <tbody>
      {% for course in courses %}
        <tr>
          <td>{{ course.title }}</td>
          <td>{{ course.category.name }}</td>
          <td>{{ course.instructor.username }}</td>
          <td>{{ course.students.count }}</td>
          <td>
            {% if course.is_published %}
              <span class="badge bg-success">Publié</span>
            {% else %}
              <span class="badge bg-warning text-dark">Brouillon</span>
            {% endif %}
          </td>
        </tr>
      {% empty %}
        <tr>
          <td colspan="5" class="text-center text-muted">Aucun cours disponible.</td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}
