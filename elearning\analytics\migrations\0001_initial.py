# Generated by Django 5.2.1 on 2025-06-25 17:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('courses', '0007_lesson_content_type_lesson_created_at_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True)),
                ('total_users', models.PositiveIntegerField(default=0)),
                ('total_students', models.PositiveIntegerField(default=0)),
                ('total_instructors', models.PositiveIntegerField(default=0)),
                ('total_courses', models.PositiveIntegerField(default=0)),
                ('total_lessons', models.PositiveIntegerField(default=0)),
                ('daily_active_users', models.PositiveIntegerField(default=0)),
                ('new_registrations', models.PositiveIntegerField(default=0)),
                ('new_enrollments', models.PositiveIntegerField(default=0)),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='CourseStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_enrollments', models.PositiveIntegerField(default=0)),
                ('total_completions', models.PositiveIntegerField(default=0)),
                ('average_completion_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('course', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='statistics', to='courses.course')),
            ],
        ),
        migrations.CreateModel(
            name='UserStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_courses_enrolled', models.PositiveIntegerField(default=0)),
                ('total_courses_completed', models.PositiveIntegerField(default=0)),
                ('total_lessons_completed', models.PositiveIntegerField(default=0)),
                ('total_time_spent', models.DurationField(blank=True, null=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='statistics', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
