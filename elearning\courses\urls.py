from django.urls import path
from . import views, course_views

urlpatterns = [
    # Cours
    path('', views.course_list, name='course_list'),
    path('<int:course_id>/', views.course_detail, name='course_detail_by_id'),
    path('by-slug/<slug:slug>/', views.course_detail_by_slug, name='course_detail_by_slug'),
    path('<int:course_id>/enroll/', course_views.enroll_course, name='enroll_course'),
    path('create/', views.create_course, name='create_course'),
    path('<int:course_id>/edit/', views.update_course, name='update_course'),
    path('<int:course_id>/delete/', views.delete_course, name='delete_course'),
    
    # Modules et leçons
    path('course/<int:course_id>/modules/', views.module_list, name='module_list'),
    path('course/<int:course_id>/modules/<int:module_id>/', views.module_detail, name='module_detail'),
    path('course/<int:course_id>/modules/<int:module_id>/lessons/<int:lesson_id>/', views.lesson_detail, name='lesson_detail'),
    path('courses/', views.course_catalog, name='course_catalog'),
    path('courses/search/', views.search_courses, name='search_courses'),
]
