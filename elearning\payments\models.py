from django.db import models
from django.conf import settings
from courses.models import Course
from django.utils import timezone


class Payment(models.Model):
    """Modèle pour gérer les paiements des étudiants"""
    
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'En attente'),
        ('completed', 'Termin<PERSON>'),
        ('failed', 'Échoué'),
        ('refunded', 'Remboursé'),
    ]
    
    PAYMENT_METHOD_CHOICES = [
        ('card', 'Carte bancaire'),
        ('paypal', 'PayPal'),
        ('bank_transfer', 'Virement bancaire'),
    ]
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payments')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='payments', null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='EUR')
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='card')
    transaction_id = models.CharField(max_length=100, unique=True, null=True, blank=True)
    payment_date = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    description = models.TextField(blank=True)
    
    # Champs pour l'intégration avec les processeurs de paiement
    stripe_payment_intent_id = models.CharField(max_length=200, null=True, blank=True)
    paypal_order_id = models.CharField(max_length=200, null=True, blank=True)
    
    class Meta:
        ordering = ['-payment_date']
    
    def __str__(self):
        return f"Paiement {self.amount}€ - {self.user.username} - {self.get_status_display()}"
    
    @property
    def is_successful(self):
        return self.status == 'completed'


class Subscription(models.Model):
    """Modèle pour gérer les abonnements des étudiants"""
    
    SUBSCRIPTION_TYPE_CHOICES = [
        ('monthly', 'Mensuel'),
        ('yearly', 'Annuel'),
        ('lifetime', 'À vie'),
    ]
    
    SUBSCRIPTION_STATUS_CHOICES = [
        ('active', 'Actif'),
        ('inactive', 'Inactif'),
        ('cancelled', 'Annulé'),
        ('expired', 'Expiré'),
    ]
    
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='subscription')
    subscription_type = models.CharField(max_length=20, choices=SUBSCRIPTION_TYPE_CHOICES, default='monthly')
    status = models.CharField(max_length=20, choices=SUBSCRIPTION_STATUS_CHOICES, default='inactive')
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True)
    auto_renew = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Abonnement {self.user.username} - {self.get_subscription_type_display()}"
    
    @property
    def is_active(self):
        return self.status == 'active' and (self.end_date is None or self.end_date > timezone.now())
    
    def get_price(self):
        """Retourne le prix de l'abonnement selon le type"""
        prices = {
            'monthly': 29.99,
            'yearly': 299.99,
            'lifetime': 999.99,
        }
        return prices.get(self.subscription_type, 0)


class Invoice(models.Model):
    """Modèle pour gérer les factures"""
    
    payment = models.OneToOneField(Payment, on_delete=models.CASCADE, related_name='invoice')
    invoice_number = models.CharField(max_length=50, unique=True)
    issue_date = models.DateTimeField(auto_now_add=True)
    due_date = models.DateTimeField()
    is_paid = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Facture {self.invoice_number} - {self.payment.user.username}"
    
    def save(self, *args, **kwargs):
        if not self.invoice_number:
            # Générer un numéro de facture unique
            import uuid
            self.invoice_number = f"INV-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        super().save(*args, **kwargs)
