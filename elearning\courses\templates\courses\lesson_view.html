{% extends 'base.html' %}
{% load static %}

{% block title %}{{ lesson.title }} - {{ course.title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .lesson-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .lesson-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .breadcrumb {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.8;
  }

  .breadcrumb a {
    color: #333;
    text-decoration: none;
  }

  .breadcrumb a:hover {
    text-decoration: underline;
  }

  .lesson-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }

  .lesson-meta {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    font-size: 0.9rem;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .lesson-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .main-content {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .lesson-video {
    width: 100%;
    height: 400px;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  }

  .lesson-text {
    line-height: 1.8;
    color: #333;
    font-size: 1.1rem;
  }

  .lesson-text h3 {
    color: #C8A8E9;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-size: 1.3rem;
  }

  .lesson-text p {
    margin-bottom: 1.5rem;
  }

  .lesson-text ul, .lesson-text ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
  }

  .lesson-text li {
    margin-bottom: 0.5rem;
  }

  .lesson-files {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
  }

  .files-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .file-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  }

  .file-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  }

  .file-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
  }

  .file-info {
    flex: 1;
  }

  .file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
  }

  .file-size {
    font-size: 0.9rem;
    color: #666;
  }

  .btn-download {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
  }

  .sidebar {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 2rem;
  }

  .progress-section {
    margin-bottom: 2rem;
  }

  .progress-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #43e97b, #38f9d7);
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  .progress-text {
    font-size: 0.9rem;
    color: #666;
    text-align: center;
  }

  .lesson-actions {
    margin-bottom: 2rem;
  }

  .btn-complete {
    width: 100%;
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
  }

  .btn-complete:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 233, 123, 0.3);
  }

  .btn-complete:disabled {
    background: #28a745;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .navigation-buttons {
    display: flex;
    gap: 0.5rem;
  }

  .btn-nav {
    flex: 1;
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn-nav:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
  }

  .btn-nav:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .course-info {
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
  }

  .course-title-sidebar {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .course-meta-sidebar {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
  }

  .btn-back-course {
    width: 100%;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn-back-course:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(200, 168, 233, 0.3);
    color: #333;
    text-decoration: none;
  }

  @media (max-width: 768px) {
    .lesson-content {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .lesson-title {
      font-size: 1.5rem;
    }

    .lesson-meta {
      gap: 1rem;
    }

    .sidebar {
      position: static;
    }

    .navigation-buttons {
      flex-direction: column;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="lesson-container">
  <!-- En-tête de la leçon -->
  <div class="lesson-header">
    <div class="breadcrumb">
      <a href="{% url 'student_dashboard' %}">Dashboard</a> /
      <a href="{% url 'course_learn' course.id %}">{{ course.title }}</a> /
      {{ lesson.module.title }}
    </div>
    <h1 class="lesson-title">{{ lesson.title }}</h1>
    <div class="lesson-meta">
      <div class="meta-item">
        <i class="fas fa-folder"></i>
        <span>{{ lesson.module.title }}</span>
      </div>
      {% if lesson.duration %}
      <div class="meta-item">
        <i class="fas fa-clock"></i>
        <span>{{ lesson.duration }} minutes</span>
      </div>
      {% endif %}
      <div class="meta-item">
        <i class="fas fa-signal"></i>
        <span>{{ course.get_level_display }}</span>
      </div>
    </div>
  </div>

  <!-- Contenu principal -->
  <div class="lesson-content">
    <div class="main-content">
      {% if lesson.video_url %}
      <video class="lesson-video" controls>
        <source src="{{ lesson.video_url }}" type="video/mp4">
        Votre navigateur ne supporte pas la lecture vidéo.
      </video>
      {% endif %}

      <div class="lesson-text">
        {{ lesson.content|linebreaks }}
      </div>

      {% if lesson.files.exists %}
      <div class="lesson-files">
        <div class="files-title">
          <i class="fas fa-download"></i>
          Ressources à télécharger
        </div>
        {% for file in lesson.files.all %}
        <div class="file-item">
          <div class="file-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="file-info">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-size">{{ file.size|filesizeformat }}</div>
          </div>
          <a href="{% url 'download_file' course.id lesson.id %}?file={{ file.id }}" 
             class="btn-download">
            <i class="fas fa-download"></i>
            Télécharger
          </a>
        </div>
        {% endfor %}
      </div>
      {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="sidebar">
      <!-- Progression -->
      <div class="progress-section">
        <div class="progress-title">
          <i class="fas fa-chart-line"></i>
          Progression du cours
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: {{ progress_percentage }}%"></div>
        </div>
        <div class="progress-text">
          {{ completed_lessons }} / {{ total_lessons }} leçons terminées
        </div>
      </div>

      <!-- Actions -->
      <div class="lesson-actions">
        <button class="btn-complete" 
                onclick="markComplete({{ course.id }}, {{ lesson.id }})"
                {% if is_completed %}disabled{% endif %}>
          {% if is_completed %}
            <i class="fas fa-check"></i> Leçon terminée
          {% else %}
            <i class="fas fa-check"></i> Marquer comme terminée
          {% endif %}
        </button>

        <div class="navigation-buttons">
          {% if previous_lesson %}
          <a href="{% url 'lesson_view' course.id previous_lesson.id %}" class="btn-nav">
            <i class="fas fa-chevron-left"></i> Précédent
          </a>
          {% else %}
          <button class="btn-nav" disabled>
            <i class="fas fa-chevron-left"></i> Précédent
          </button>
          {% endif %}

          {% if next_lesson %}
          <a href="{% url 'lesson_view' course.id next_lesson.id %}" class="btn-nav">
            Suivant <i class="fas fa-chevron-right"></i>
          </a>
          {% else %}
          <button class="btn-nav" disabled>
            Suivant <i class="fas fa-chevron-right"></i>
          </button>
          {% endif %}
        </div>
      </div>

      <!-- Informations du cours -->
      <div class="course-info">
        <div class="course-title-sidebar">{{ course.title }}</div>
        <div class="course-meta-sidebar">
          Par {{ course.instructor.get_full_name|default:course.instructor.username }}
        </div>
        <a href="{% url 'course_learn' course.id %}" class="btn-back-course">
          <i class="fas fa-arrow-left"></i>
          Retour au cours
        </a>
      </div>
    </div>
  </div>
</div>

<script>
function markComplete(courseId, lessonId) {
  fetch(`/course/${courseId}/lesson/${lessonId}/complete/`, {
    method: 'POST',
    headers: {
      'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
      'Content-Type': 'application/json',
    },
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      location.reload();
    } else {
      alert('Erreur lors de la mise à jour du statut de la leçon');
    }
  })
  .catch(error => {
    console.error('Erreur:', error);
    alert('Erreur de connexion');
  });
}
</script>

{% csrf_token %}
{% endblock %}
