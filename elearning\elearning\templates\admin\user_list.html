{% extends 'base.html' %}
{% load static %}

{% block title %}Gestion des Utilisateurs | E-Learn+{% endblock %}

{% block content %}
<div class="container">
  <div class="admin-header">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-6">
          <h1 class="admin-title">
            <i class="fas fa-users me-2"></i>
            Gestion des Utilisateurs
          </h1>
          <p class="admin-welcome">Gérez les utilisateurs de votre plateforme d'apprentissage.</p>
        </div>
        <div class="col-md-6">
          <div class="admin-nav">
            <a href="{% url 'dashboard' %}" class="admin-nav-btn">
              <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{% url 'admin_courses' %}" class="admin-nav-btn">
              <i class="fas fa-book"></i> Cours
            </a>
            <a href="{% url 'admin_settings' %}" class="admin-nav-btn">
              <i class="fas fa-cog"></i> Paramètres
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Liste des utilisateurs</h5>
      <a href="{% url 'add_instructor' %}" class="btn btn-primary btn-sm">
        <i class="fas fa-plus"></i> Ajouter un instructeur
      </a>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Nom d'utilisateur</th>
              <th>Email</th>
              <th>Rôle</th>
              <th>Date d'inscription</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for user in users %}
            <tr>
              <td>{{ user.username }}</td>
              <td>{{ user.email }}</td>
              <td>
                <span class="badge {% if user.role == 'admin' %}bg-danger{% elif user.role == 'instructor' %}bg-primary{% else %}bg-success{% endif %}">
                  {{ user.get_role_display }}
                </span>
              </td>
              <td>{{ user.date_joined|date:"d/m/Y" }}</td>
              <td>
                <div class="btn-group">
                  <a href="#" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-edit"></i>
                  </a>
                  <a href="#" class="btn btn-sm btn-outline-danger">
                    <i class="fas fa-trash"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="5" class="text-center">Aucun utilisateur trouvé</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{% endblock %}