from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class Conversation(models.Model):
    """Modèle pour gérer les conversations entre utilisateurs"""
    participants = models.ManyToManyField(User, related_name='conversations')
    subject = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Pour identifier le type de conversation
    course = models.ForeignKey('courses.Course', on_delete=models.CASCADE, null=True, blank=True,
                              help_text="Cours lié à la conversation")

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.subject} - {self.participants.count()} participants"

    def get_last_message(self):
        """Récupère le dernier message de la conversation"""
        return self.messages.order_by('-created_at').first()

    def get_other_participant(self, user):
        """Récupère l'autre participant dans une conversation à 2 personnes"""
        return self.participants.exclude(id=user.id).first()

    def mark_as_read(self, user):
        """Marque tous les messages comme lus pour un utilisateur"""
        self.messages.filter(is_read=False).exclude(sender=user).update(is_read=True)


class Message(models.Model):
    """Modèle pour les messages individuels"""
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    # Pour les pièces jointes (optionnel)
    attachment = models.FileField(upload_to='message_attachments/', null=True, blank=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"{self.sender.username}: {self.content[:50]}..."

    def mark_as_read(self):
        """Marque le message comme lu"""
        if not self.is_read:
            self.is_read = True
            self.save()
