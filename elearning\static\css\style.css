/* Remplacer le gradient bleu foncé par une palette plus claire */
:root {
  --primary: #6c9bcf;
  --secondary: #8bb9e0;
  --success: #2ecc71;
  --info: #4cc9f0;
  --warning: #f8961e;
  --danger: #f94144;
  --light: #f8f9fa;
  --dark: #212529;
  --white: #ffffff;
  --gradient-primary: linear-gradient(135deg, #8bb9e0, #6c9bcf);
  --gradient-secondary: linear-gradient(135deg, #f8a5c2, #f78fb3);
  --gradient-success: linear-gradient(135deg, #2ecc71, #27ae60);
  --gradient-info: linear-gradient(135deg, #4cc9f0, #3498db);
  --gradient-warning: linear-gradient(135deg, #f8961e, #f39c12);
  --gradient-danger: linear-gradient(135deg, #f94144, #e74c3c);
  --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --border-radius-sm: 8px;
  --transition: all 0.3s ease;
}

body {
  background-color: #f5f7fa;
  font-family: 'Poppins', sans-serif;
}

/* Barre de navigation colorée et moderne */
.navbar {
  background: var(--gradient-primary) !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 15px 0;
}

.navbar-brand {
  font-weight: 700;
  color: white !important;
}

.navbar .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600;
  transition: var(--transition);
  padding: 8px 15px;
  border-radius: 50px;
}

.navbar .nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* Bouton d'ajout de cours plus visible */
.btn-add-course {
  background: var(--gradient-secondary);
  color: white !important;
  border: none;
  border-radius: 50px;
  padding: 12px 25px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 15px rgba(247, 37, 133, 0.3);
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
}

.btn-add-course i {
  margin-right: 8px;
}

.btn-add-course:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(247, 37, 133, 0.4);
}

/* Boutons d'action (voir, modifier, supprimer) */
.btn-view {
  background: var(--gradient-primary);
  color: white !important;
  border: none;
  border-radius: 50px;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.2);
  text-decoration: none;
}

.btn-view:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(67, 97, 238, 0.3);
}

.btn-edit {
  background: var(--gradient-warning);
  color: white !important;
  border: none;
  border-radius: 50px;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(248, 150, 30, 0.2);
  text-decoration: none;
}

.btn-edit:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(248, 150, 30, 0.3);
}

.btn-delete {
  background: var(--gradient-danger);
  color: white !important;
  border: none;
  border-radius: 50px;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(249, 65, 68, 0.2);
  text-decoration: none;
}

.btn-delete:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(249, 65, 68, 0.3);
}

/* Liste des cours améliorée */
.course-list {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  margin-top: 20px;
}

.course-list-item {
  padding: 20px;
  border-bottom: 1px solid #eee;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.course-list-item:hover {
  background-color: #f8f9fa;
  transform: translateX(5px);
}

.course-info {
  flex: 1;
}

.course-title {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--dark);
  margin-bottom: 5px;
}

.course-description {
  color: #666;
  margin-bottom: 10px;
}

.course-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 0.9rem;
  color: #777;
}

.course-meta i {
  color: var(--primary);
}

.course-actions {
  display: flex;
  gap: 10px;
}

/* En-tête de page avec dégradé */
.page-header {
  margin: 30px 0;
  position: relative;
  padding-bottom: 15px;
  font-size: 2.5rem;
  font-weight: 800;
  color: #333;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(67, 97, 238, 0.1);
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

/* Bouton de déconnexion */
.btn-logout {
  background-color: rgba(255, 255, 255, 0.15);
  color: white !important;
  border-radius: 50px;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
  text-decoration: none;
}

.btn-logout:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Badges de niveau */
.level-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.level-beginner {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.level-intermediate {
  background-color: rgba(248, 150, 30, 0.2);
  color: #f8961e;
}

.level-advanced {
  background-color: rgba(249, 65, 68, 0.2);
  color: #f94144;
}

/* Formulaire de recherche */
.search-form {
  margin-bottom: 20px;
}

.search-input {
  border-radius: 50px 0 0 50px;
  padding: 12px 20px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.search-btn {
  border-radius: 0 50px 50px 0;
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 0 20px;
}

.filter-select {
  border-radius: 50px;
  padding: 10px 20px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Animations pour les boutons */
.btn-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Mode sombre */
.dark-mode {
  background-color: #121212;
  color: #e0e0e0;
}

.dark-mode .navbar {
  background: linear-gradient(135deg, #2c3e50, #1a1a2e) !important;
}

.dark-mode .course-card,
.dark-mode .filters-container,
.dark-mode .course-list {
  background-color: #1e1e1e;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.dark-mode .course-card .card-title {
  color: #f0f0f0;
}

.dark-mode .course-card .card-text {
  color: #b0b0b0;
}

.dark-mode .course-card .course-meta {
  background-color: #252525;
  border-top: 1px solid #333;
}

.dark-mode .course-list-item {
  border-bottom: 1px solid #333;
}

.dark-mode .course-list-item:hover {
  background-color: #252525;
}

.dark-mode .course-list-item .course-title {
  color: #f0f0f0;
}

.dark-mode .course-list-item .course-description {
  color: #b0b0b0;
}

/* Bouton de basculement du mode sombre */
.dark-mode-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--gradient-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

/* Cibler spécifiquement les en-têtes de cours */
.course-header, 
.course-banner, 
.course-hero, 
.course-detail-header,
.course-info-header {
  background: linear-gradient(135deg, #ffd6e0, #c8e7ff) !important;
  color: #333 !important;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Améliorer la lisibilité du texte */
.course-header h1, 
.course-header h2, 
.course-title,
.course-banner h1,
.course-banner h2,
.course-header p,
.course-description {
  color: #333 !important;
  font-weight: 600;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

/* Ajouter des accents visuels modernes */
.course-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="2"/></svg>');
  background-size: 150px;
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

/* Styles pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input[type="text"], 
input[type="number"], 
textarea, 
select {
  border: none;
  border-bottom: 2px solid #e0e0e0;
  border-radius: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  font-size: 1rem;
  width: 100%;
  margin-bottom: 25px;
}

input[type="text"]:focus, 
input[type="number"]:focus, 
textarea:focus, 
select:focus {
  border-bottom: 2px solid #4361ee;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

/* Labels flottants */
label {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 600;
  transition: all 0.3s;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Style pour le sélecteur de fichier */
input[type="file"] {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

input[type="file"]:hover {
  border-color: #4361ee;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* Checkbox stylisée */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4361ee;
  margin-right: 10px;
}

/* Boutons d'action */
.btn, button, input[type="submit"] {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  cursor: pointer;
}

/* Bouton principal */
.btn-primary, input[type="submit"], button[type="submit"], #enregistrer {
  background: linear-gradient(135deg, #4361ee, #3a0ca3);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover, input[type="submit"]:hover, button[type="submit"]:hover, #enregistrer:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

/* Bouton secondaire */
.btn-secondary, button[type="button"], #annuler {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #495057;
}

.btn-secondary:hover, button[type="button"]:hover, #annuler:hover {
  transform: translateY(-3px);
  background: linear-gradient(135deg, #dee2e6, #ced4da);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Espacement et mise en page */
.form-group, .form-row, .form-field {
  margin-bottom: 25px;
  padding: 0 20px;
}

/* Animation sur focus */
input:focus, textarea:focus, select:focus {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Style audacieux pour le formulaire de modification de cours */
.form-container, form {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* En-tête du formulaire avec dégradé */
form h2, .form-header, .card-header, .form-title {
  background: linear-gradient(135deg, #4361ee, #7209b7);
  color: white;
  padding: 20px;
  margin: 0 0 20px 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Champs de formulaire avec style moderne */
input

