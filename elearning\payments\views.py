from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
import json
import uuid

from .models import Payment, Subscription, Invoice
from courses.models import Course
from users.models import User


@login_required
def payment_history(request):
    """Affiche l'historique des paiements de l'utilisateur"""
    payments = Payment.objects.filter(user=request.user).order_by('-payment_date')
    
    context = {
        'payments': payments,
        'title': 'Historique des paiements'
    }
    return render(request, 'payments/payment_history.html', context)


@login_required
def course_payment(request, course_id):
    """Page de paiement pour un cours"""
    course = get_object_or_404(Course, id=course_id)
    
    # Vérifier si l'utilisateur a déjà payé pour ce cours
    existing_payment = Payment.objects.filter(
        user=request.user, 
        course=course, 
        status='completed'
    ).first()
    
    if existing_payment:
        messages.info(request, "Vous avez déjà payé pour ce cours.")
        return redirect('course_detail_by_id', course_id=course.id)
    
    # Vérifier si le cours est gratuit
    if course.is_free or course.price == 0:
        # Inscription directe pour les cours gratuits
        course.students.add(request.user)
        messages.success(request, f"Vous êtes maintenant inscrit au cours '{course.title}'!")
        return redirect('course_detail_by_id', course_id=course.id)
    
    context = {
        'course': course,
        'title': f'Paiement - {course.title}'
    }
    return render(request, 'payments/course_payment.html', context)


@login_required
def process_course_payment(request, course_id):
    """Traite le paiement d'un cours"""
    if request.method != 'POST':
        return redirect('course_payment', course_id=course_id)
    
    course = get_object_or_404(Course, id=course_id)
    
    # Simuler le traitement du paiement (à remplacer par une vraie intégration)
    payment_method = request.POST.get('payment_method', 'card')
    
    try:
        # Créer le paiement
        payment = Payment.objects.create(
            user=request.user,
            course=course,
            amount=course.price,
            payment_method=payment_method,
            status='completed',  # En production, ce serait 'pending' puis mis à jour
            transaction_id=str(uuid.uuid4()),
            description=f"Paiement pour le cours: {course.title}"
        )
        
        # Inscrire l'étudiant au cours
        course.students.add(request.user)
        
        # Créer la facture
        invoice = Invoice.objects.create(
            payment=payment,
            due_date=timezone.now() + timedelta(days=30),
            is_paid=True
        )
        
        messages.success(request, f"Paiement réussi! Vous êtes maintenant inscrit au cours '{course.title}'.")
        return redirect('course_detail_by_id', course_id=course.id)
        
    except Exception as e:
        messages.error(request, "Une erreur est survenue lors du paiement. Veuillez réessayer.")
        return redirect('course_payment', course_id=course_id)


@login_required
def subscription_page(request):
    """Page de gestion des abonnements"""
    try:
        subscription = request.user.subscription
    except Subscription.DoesNotExist:
        subscription = None
    
    context = {
        'subscription': subscription,
        'title': 'Mon abonnement'
    }
    return render(request, 'payments/subscription.html', context)


@login_required
def subscribe(request, subscription_type):
    """Processus d'abonnement"""
    if subscription_type not in ['monthly', 'yearly', 'lifetime']:
        messages.error(request, "Type d'abonnement invalide.")
        return redirect('subscription_page')
    
    # Vérifier si l'utilisateur a déjà un abonnement actif
    try:
        existing_subscription = request.user.subscription
        if existing_subscription.is_active:
            messages.info(request, "Vous avez déjà un abonnement actif.")
            return redirect('subscription_page')
    except Subscription.DoesNotExist:
        pass
    
    if request.method == 'POST':
        try:
            # Créer ou mettre à jour l'abonnement
            subscription, created = Subscription.objects.get_or_create(
                user=request.user,
                defaults={
                    'subscription_type': subscription_type,
                    'status': 'active',
                    'start_date': timezone.now(),
                }
            )
            
            if not created:
                subscription.subscription_type = subscription_type
                subscription.status = 'active'
                subscription.start_date = timezone.now()
            
            # Définir la date de fin selon le type
            if subscription_type == 'monthly':
                subscription.end_date = timezone.now() + timedelta(days=30)
            elif subscription_type == 'yearly':
                subscription.end_date = timezone.now() + timedelta(days=365)
            else:  # lifetime
                subscription.end_date = None
            
            subscription.save()
            
            # Créer le paiement
            payment = Payment.objects.create(
                user=request.user,
                amount=subscription.get_price(),
                payment_method=request.POST.get('payment_method', 'card'),
                status='completed',
                transaction_id=str(uuid.uuid4()),
                description=f"Abonnement {subscription.get_subscription_type_display()}"
            )
            
            messages.success(request, f"Abonnement {subscription.get_subscription_type_display()} activé avec succès!")
            return redirect('subscription_page')
            
        except Exception as e:
            messages.error(request, "Une erreur est survenue lors de l'abonnement. Veuillez réessayer.")
    
    # Afficher la page de paiement pour l'abonnement
    subscription_prices = {
        'monthly': 29.99,
        'yearly': 299.99,
        'lifetime': 999.99,
    }
    
    context = {
        'subscription_type': subscription_type,
        'price': subscription_prices[subscription_type],
        'title': f'Abonnement {subscription_type.title()}'
    }
    return render(request, 'payments/subscribe.html', context)


@login_required
def invoice_detail(request, invoice_id):
    """Affiche le détail d'une facture"""
    invoice = get_object_or_404(Invoice, id=invoice_id, payment__user=request.user)
    
    context = {
        'invoice': invoice,
        'title': f'Facture {invoice.invoice_number}'
    }
    return render(request, 'payments/invoice_detail.html', context)
