{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Gestion des Leçons | {{ module.title }} | E-Learn+{% endblock %}

{% block content %}
<div class="container py-4">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
      <li class="breadcrumb-item"><a href="{% url 'admin_courses' %}">Cours</a></li>
      <li class="breadcrumb-item"><a href="{% url 'manage_modules' module.course.id %}">{{ module.course.title }}</a></li>
      <li class="breadcrumb-item active">Leçons de {{ module.title }}</li>
    </ol>
  </nav>

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Leçons du module: {{ module.title }}</h2>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLessonModal">
      <i class="fas fa-plus me-1"></i> Ajouter une leçon
    </button>
  </div>

  <!-- Liste des leçons -->
  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <div class="list-group">
        {% for lesson in lessons %}
        <div class="list-group-item list-group-item-action">
          <div class="d-flex w-100 justify-content-between">
            <h5 class="mb-1">{{ lesson.title }}</h5>
            <div class="btn-group">
              <a href="{% url 'update_lesson' lesson.id %}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-edit"></i>
              </a>
              <a href="{% url 'delete_lesson' lesson.id %}" class="btn btn-sm btn-outline-danger">
                <i class="fas fa-trash"></i>
              </a>
            </div>
          </div>
          <p class="mb-1">{{ lesson.content|truncatewords:30 }}</p>
          {% if lesson.video_url %}
          <small class="text-muted">
            <i class="fas fa-video me-1"></i> Vidéo disponible
          </small>
          {% endif %}
        </div>
        {% empty %}
        <div class="text-center py-4">
          <p class="text-muted mb-3">Aucune leçon trouvée pour ce module</p>
          <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLessonModal">
            <i class="fas fa-plus me-1"></i> Ajouter une leçon
          </button>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Modal pour ajouter une leçon -->
  <div class="modal fade" id="addLessonModal" tabindex="-1" aria-labelledby="addLessonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addLessonModalLabel">Ajouter une leçon</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form method="post" action="{% url 'create_lesson' module.id %}">
          <div class="modal-body">
            {% csrf_token %}
            {{ lesson_form|crispy }}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="submit" class="btn btn-primary">Ajouter</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}