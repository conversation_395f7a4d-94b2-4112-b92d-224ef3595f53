from django.urls import path
from . import views

urlpatterns = [
    path('users/', views.user_list, name='list_users'),
    path('create-user/', views.create_user, name='create_user'),
    path('delete-user/<int:user_id>/', views.delete_user, name='delete_user'),
    path('register/', views.register_view, name='register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('dashboard/', views.admin_dashboard, name='dashboard'),
    path('profile/', views.profile_view, name='profile'),
    path('dashboard/add-instructor/', views.add_instructor_view, name='add_instructor'),
    path('dashboard/add-user/', views.add_user, name='add_user'),
]
