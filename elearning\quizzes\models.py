from django.db import models
from courses.models import Course
from users.models import User

class Quiz(models.Model):
    CORRECTION_CHOICES = [
        ('auto', 'Correction automatique'),
        ('manual', 'Correction manuelle'),
    ]

    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='quizzes')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    correction_type = models.CharField(max_length=10, choices=CORRECTION_CHOICES, default='auto')
    time_limit = models.PositiveIntegerField(null=True, blank=True, help_text="Temps limite en minutes")
    max_attempts = models.PositiveIntegerField(default=1, help_text="Nombre maximum de tentatives")
    passing_score = models.PositiveIntegerField(default=60, help_text="Score minimum pour réussir (%)")
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('users.User', on_delete=models.CASCADE, related_name='created_quizzes')

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Quizzes"

    def __str__(self):
        return f"{self.title} ({self.get_correction_type_display()})"

    def get_total_questions(self):
        return self.questions.count()

    def get_max_score(self):
        return self.questions.count()  # 1 point par question

    def is_auto_corrected(self):
        return self.correction_type == 'auto'

class Question(models.Model):
    QUESTION_TYPES = [
        ('multiple_choice', 'Choix multiple'),
        ('true_false', 'Vrai/Faux'),
        ('short_answer', 'Réponse courte'),
        ('essay', 'Dissertation'),
    ]

    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='questions')
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES, default='multiple_choice')
    text = models.TextField()
    points = models.PositiveIntegerField(default=1)
    order = models.PositiveIntegerField(default=0)

    # Pour les questions à choix multiples
    option_a = models.CharField(max_length=255, blank=True)
    option_b = models.CharField(max_length=255, blank=True)
    option_c = models.CharField(max_length=255, blank=True)
    option_d = models.CharField(max_length=255, blank=True)
    correct_option = models.CharField(max_length=1, choices=[
        ('A', 'A'), ('B', 'B'), ('C', 'C'), ('D', 'D')
    ], blank=True)

    # Pour les questions vrai/faux
    correct_answer_boolean = models.BooleanField(null=True, blank=True)

    # Pour les réponses courtes
    correct_answer_text = models.TextField(blank=True)

    # Explication de la réponse
    explanation = models.TextField(blank=True, help_text="Explication de la bonne réponse")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.quiz.title} - {self.text[:50]}..."

    def is_multiple_choice(self):
        return self.question_type == 'multiple_choice'

    def is_true_false(self):
        return self.question_type == 'true_false'

    def is_short_answer(self):
        return self.question_type == 'short_answer'

    def is_essay(self):
        return self.question_type == 'essay'

    def requires_manual_correction(self):
        return self.question_type in ['short_answer', 'essay']

class QuizSubmission(models.Model):
    STATUS_CHOICES = [
        ('in_progress', 'En cours'),
        ('submitted', 'Soumis'),
        ('graded', 'Corrigé'),
    ]

    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='submissions')
    student = models.ForeignKey(User, on_delete=models.CASCADE, limit_choices_to={'role': 'student'}, related_name='quiz_submissions')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='in_progress')
    score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    max_score = models.PositiveIntegerField(default=0)
    percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    attempt_number = models.PositiveIntegerField(default=1)
    time_taken = models.DurationField(null=True, blank=True)
    started_at = models.DateTimeField(auto_now_add=True)
    submitted_at = models.DateTimeField(null=True, blank=True)
    graded_at = models.DateTimeField(null=True, blank=True)
    graded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='graded_submissions')
    feedback = models.TextField(blank=True)

    class Meta:
        ordering = ['-started_at']
        unique_together = ['quiz', 'student', 'attempt_number']

    def __str__(self):
        return f"{self.student.username} - {self.quiz.title} (Tentative {self.attempt_number})"

    def is_passed(self):
        if self.percentage is None:
            return False
        return self.percentage >= self.quiz.passing_score

    def needs_grading(self):
        return self.status == 'submitted' and self.quiz.correction_type == 'manual'

    def calculate_percentage(self):
        if self.score is not None and self.max_score > 0:
            self.percentage = (self.score / self.max_score) * 100
            return self.percentage
        return 0


class QuizAnswer(models.Model):
    """Modèle pour stocker les réponses individuelles aux questions"""
    submission = models.ForeignKey(QuizSubmission, on_delete=models.CASCADE, related_name='answers')
    question = models.ForeignKey(Question, on_delete=models.CASCADE)

    # Différents types de réponses
    selected_option = models.CharField(max_length=1, blank=True)  # Pour choix multiples
    boolean_answer = models.BooleanField(null=True, blank=True)  # Pour vrai/faux
    text_answer = models.TextField(blank=True)  # Pour réponses courtes et dissertations

    # Correction
    is_correct = models.BooleanField(null=True, blank=True)
    points_awarded = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    instructor_feedback = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['submission', 'question']

    def __str__(self):
        return f"{self.submission.student.username} - {self.question.text[:30]}..."

    def auto_grade(self):
        """Correction automatique pour les questions qui le permettent"""
        if self.question.is_multiple_choice():
            self.is_correct = self.selected_option == self.question.correct_option
        elif self.question.is_true_false():
            self.is_correct = self.boolean_answer == self.question.correct_answer_boolean
        elif self.question.is_short_answer() and self.question.correct_answer_text:
            # Comparaison simple pour les réponses courtes
            self.is_correct = self.text_answer.strip().lower() == self.question.correct_answer_text.strip().lower()

        if self.is_correct:
            self.points_awarded = self.question.points
        else:
            self.points_awarded = 0

        self.save()
        return self.is_correct

