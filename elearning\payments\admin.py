from django.contrib import admin
from .models import Payment, Subscription, Invoice


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['user', 'course', 'amount', 'status', 'payment_method', 'payment_date']
    list_filter = ['status', 'payment_method', 'payment_date']
    search_fields = ['user__username', 'user__email', 'transaction_id']
    readonly_fields = ['payment_date', 'updated_at']
    
    fieldsets = (
        ('Informations de base', {
            'fields': ('user', 'course', 'amount', 'currency', 'description')
        }),
        ('Statut du paiement', {
            'fields': ('status', 'payment_method', 'transaction_id')
        }),
        ('Intégrations externes', {
            'fields': ('stripe_payment_intent_id', 'paypal_order_id'),
            'classes': ('collapse',)
        }),
        ('Dates', {
            'fields': ('payment_date', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ['user', 'subscription_type', 'status', 'start_date', 'end_date', 'is_active']
    list_filter = ['subscription_type', 'status', 'auto_renew']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'payment', 'issue_date', 'due_date', 'is_paid']
    list_filter = ['is_paid', 'issue_date']
    search_fields = ['invoice_number', 'payment__user__username']
    readonly_fields = ['issue_date']
