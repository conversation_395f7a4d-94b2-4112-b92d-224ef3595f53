from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from users.models import User
from courses.models import Course, Module, Lesson
from courses.forms import CourseForm, ModuleForm, LessonForm

def is_admin(user):
    return user.is_authenticated and user.role == 'admin'

@login_required
@user_passes_test(is_admin)
def admin_dashboard(request):
    """Vue pour le tableau de bord administrateur."""
    courses = Course.objects.all().order_by('-created_at')[:5]
    students = User.objects.filter(role='student').order_by('-date_joined')[:5]
    instructors = User.objects.filter(role='instructor').order_by('-date_joined')[:5]

    context = {
        'active_tab': 'dashboard',
        'courses': courses,
        'students': students,
        'instructors': instructors,
        'course_count': Course.objects.count(),
        'student_count': User.objects.filter(role='student').count(),
        'instructor_count': User.objects.filter(role='instructor').count(),
        'module_count': Module.objects.count(),
    }

    return render(request, 'users/admin_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_students(request):
    """Vue pour gérer les étudiants."""
    students = User.objects.filter(role='student').order_by('-date_joined')
    
    context = {
        'active_tab': 'students',
        'students': students,
    }
    
    return render(request, 'courses/admin_student_management.html', context)

@login_required
@user_passes_test(is_admin)
def admin_instructors(request):
    """Vue pour gérer les instructeurs."""
    if request.method == 'POST':
        # Traitement du formulaire d'ajout d'instructeur
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        
        if username and email and password:
            # Vérifier si l'utilisateur existe déjà
            if User.objects.filter(username=username).exists():
                messages.error(request, f"Un utilisateur avec le nom '{username}' existe déjà.")
            elif User.objects.filter(email=email).exists():
                messages.error(request, f"Un utilisateur avec l'email '{email}' existe déjà.")
            else:
                # Créer un nouvel instructeur
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password,
                    role='instructor'
                )
                messages.success(request, f"L'instructeur '{username}' a été créé avec succès.")
                return redirect('admin_instructors')
        else:
            messages.error(request, "Veuillez remplir tous les champs obligatoires.")
    
    instructors = User.objects.filter(role='instructor').order_by('-date_joined')
    
    context = {
        'active_tab': 'instructors',
        'instructors': instructors,
    }
    
    return render(request, 'courses/admin_instructor_management.html', context)

@login_required
@user_passes_test(is_admin)
def course_list(request):
    """Vue pour gérer les cours."""
    courses = Course.objects.all().order_by('-created_at')
    
    context = {
        'active_tab': 'courses',
        'courses': courses,
    }
    
    return render(request, 'courses/admin_course_management.html', context)

@login_required
@user_passes_test(is_admin)
def create_course(request):
    """Vue pour créer un nouveau cours."""
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES)  # Ajout de request.FILES pour gérer les images
        if form.is_valid():
            course = form.save(commit=False)
            # Assurez-vous que l'instructeur est défini
            if not course.instructor:
                # Si l'administrateur crée le cours, il peut choisir un instructeur
                # Sinon, l'administrateur devient l'instructeur par défaut
                course.instructor = request.user
            course.save()
            messages.success(request, "Le cours a été créé avec succès.")
            return redirect('admin_courses')
    else:
        form = CourseForm()
    
    return render(request, 'courses/admin_course_form.html', {'form': form, 'title': 'Créer un cours'})

@login_required
@user_passes_test(is_admin)
def update_course(request, course_id):
    """Vue pour modifier un cours existant."""
    course = get_object_or_404(Course, id=course_id)
    
    if request.method == 'POST':
        form = CourseForm(request.POST, instance=course)
        if form.is_valid():
            form.save()
            messages.success(request, "Le cours a été mis à jour avec succès.")
            return redirect('admin_courses')
    else:
        form = CourseForm(instance=course)
    
    return render(request, 'courses/admin_course_form.html', {'form': form, 'title': 'Modifier le cours'})

@login_required
@user_passes_test(is_admin)
def delete_course(request, course_id):
    """Vue pour supprimer un cours."""
    course = get_object_or_404(Course, id=course_id)
    
    if request.method == 'POST':
        course.delete()
        messages.success(request, "Le cours a été supprimé avec succès.")
        return redirect('admin_courses')
    
    return render(request, 'courses/admin_confirm_delete.html', {'object': course, 'title': 'Supprimer le cours'})

@login_required
@user_passes_test(is_admin)
def manage_modules(request, course_id):
    """Vue pour gérer les modules d'un cours."""
    course = get_object_or_404(Course, id=course_id)
    modules = Module.objects.filter(course=course).order_by('order')
    
    context = {
        'course': course,
        'modules': modules,
    }
    
    return render(request, 'courses/admin_module_management.html', context)

@login_required
@user_passes_test(is_admin)
def create_module(request, course_id=None):
    """Vue pour créer un nouveau module."""
    if request.method == 'POST':
        form = ModuleForm(request.POST)
        if form.is_valid():
            module = form.save(commit=False)
            
            # Si course_id est fourni dans l'URL, utiliser ce cours
            if course_id:
                course = get_object_or_404(Course, id=course_id)
                module.course = course
            # Sinon, utiliser le cours sélectionné dans le formulaire
            else:
                course_id = request.POST.get('course')
                if course_id:
                    course = get_object_or_404(Course, id=course_id)
                    module.course = course
                else:
                    messages.error(request, "Veuillez sélectionner un cours.")
                    return redirect('admin_courses')
            
            module.save()
            messages.success(request, "Le module a été créé avec succès.")
            return redirect('manage_modules', course_id=module.course.id)
    else:
        form = ModuleForm()
        if course_id:
            course = get_object_or_404(Course, id=course_id)
            context = {'form': form, 'course': course, 'title': 'Créer un module'}
            return render(request, 'courses/admin_module_form.html', context)
    
    # Si la méthode est GET sans course_id, rediriger vers la liste des cours
    return redirect('admin_courses')

@login_required
@user_passes_test(is_admin)
def update_module(request, module_id):
    """Vue pour modifier un module existant."""
    module = get_object_or_404(Module, id=module_id)
    
    if request.method == 'POST':
        form = ModuleForm(request.POST, instance=module)
        if form.is_valid():
            form.save()
            messages.success(request, "Le module a été mis à jour avec succès.")
            return redirect('manage_modules', course_id=module.course.id)
    else:
        form = ModuleForm(instance=module)
    
    return render(request, 'courses/admin_module_form.html', {'form': form, 'module': module, 'title': 'Modifier le module'})

@login_required
@user_passes_test(is_admin)
def delete_module(request, module_id):
    """Vue pour supprimer un module."""
    module = get_object_or_404(Module, id=module_id)
    course_id = module.course.id
    
    if request.method == 'POST':
        module.delete()
        messages.success(request, "Le module a été supprimé avec succès.")
        return redirect('manage_modules', course_id=course_id)
    
    return render(request, 'courses/admin_confirm_delete.html', {'object': module, 'title': 'Supprimer le module'})

@login_required
@user_passes_test(is_admin)
def manage_lessons(request, module_id):
    """Vue pour gérer les leçons d'un module."""
    module = get_object_or_404(Module, id=module_id)
    lessons = Lesson.objects.filter(module=module).order_by('order')
    
    context = {
        'module': module,
        'lessons': lessons,
    }
    
    return render(request, 'courses/admin_lesson_management.html', context)

@login_required
@user_passes_test(is_admin)
def create_lesson(request, module_id=None):
    """Vue pour créer une nouvelle leçon."""
    if request.method == 'POST':
        form = LessonForm(request.POST, request.FILES)
        if form.is_valid():
            lesson = form.save(commit=False)
            
            # Si module_id est fourni dans l'URL, utiliser ce module
            if module_id:
                module = get_object_or_404(Module, id=module_id)
                lesson.module = module
            # Sinon, utiliser le module sélectionné dans le formulaire
            else:
                module_id = request.POST.get('module')
                if module_id:
                    module = get_object_or_404(Module, id=module_id)
                    lesson.module = module
                else:
                    messages.error(request, "Veuillez sélectionner un module.")
                    return redirect('admin_courses')
            
            # Gérer le type de contenu
            content_type = request.POST.get('content_type', 'text')
            lesson.content_type = content_type
            
            lesson.save()
            messages.success(request, "La leçon a été créée avec succès.")
            return redirect('manage_lessons', module_id=lesson.module.id)
    else:
        form = LessonForm()
        if module_id:
            module = get_object_or_404(Module, id=module_id)
            context = {'form': form, 'module': module, 'title': 'Créer une leçon'}
            return render(request, 'courses/admin_lesson_form.html', context)
    
    # Si la méthode est GET sans module_id, rediriger vers la liste des cours
    return redirect('admin_courses')

@login_required
@user_passes_test(is_admin)
def update_lesson(request, lesson_id):
    """Vue pour modifier une leçon existante."""
    lesson = get_object_or_404(Lesson, id=lesson_id)
    
    if request.method == 'POST':
        form = LessonForm(request.POST, instance=lesson)
        if form.is_valid():
            form.save()
            messages.success(request, "La leçon a été mise à jour avec succès.")
            return redirect('manage_lessons', module_id=lesson.module.id)
    else:
        form = LessonForm(instance=lesson)
    
    return render(request, 'courses/admin_lesson_form.html', {'form': form, 'lesson': lesson, 'title': 'Modifier la leçon'})

@login_required
@user_passes_test(is_admin)
def delete_lesson(request, lesson_id):
    """Vue pour supprimer une leçon."""
    lesson = get_object_or_404(Lesson, id=lesson_id)
    module_id = lesson.module.id
    
    if request.method == 'POST':
        lesson.delete()
        messages.success(request, "La leçon a été supprimée avec succès.")
        return redirect('manage_lessons', module_id=module_id)
    
    return render(request, 'courses/admin_confirm_delete.html', {'object': lesson, 'title': 'Supprimer la leçon'})

@login_required
@user_passes_test(is_admin)
def settings(request):
    """Vue pour les paramètres du site."""
    return render(request, 'courses/admin_settings.html', {'active_tab': 'settings'})

@login_required
@user_passes_test(is_admin)
def manage_user_roles(request):
    """Vue pour gérer les rôles des utilisateurs."""
    users = User.objects.all().order_by('-date_joined')

    if request.method == 'POST':
        user_id = request.POST.get('user_id')
        new_role = request.POST.get('role')

        if user_id and new_role in ['student', 'instructor', 'admin']:
            try:
                user = User.objects.get(id=user_id)
                user.role = new_role
                user.save()
                messages.success(request, f"Rôle de {user.username} mis à jour vers {user.get_role_display()}")
            except User.DoesNotExist:
                messages.error(request, "Utilisateur introuvable")
        else:
            messages.error(request, "Données invalides")

        return redirect('manage_user_roles')

    context = {
        'users': users,
        'active_tab': 'roles'
    }
    return render(request, 'courses/admin_user_roles.html', context)

@login_required
@user_passes_test(is_admin)
def bulk_user_actions(request):
    """Vue pour les actions en lot sur les utilisateurs."""
    if request.method == 'POST':
        action = request.POST.get('action')
        user_ids = request.POST.getlist('user_ids')

        if not user_ids:
            messages.error(request, "Aucun utilisateur sélectionné")
            return redirect('admin_users')

        users = User.objects.filter(id__in=user_ids)

        if action == 'activate':
            users.update(is_active=True)
            messages.success(request, f"{users.count()} utilisateurs activés")
        elif action == 'deactivate':
            users.update(is_active=False)
            messages.success(request, f"{users.count()} utilisateurs désactivés")
        elif action == 'delete':
            count = users.count()
            users.delete()
            messages.success(request, f"{count} utilisateurs supprimés")
        else:
            messages.error(request, "Action invalide")

    return redirect('admin_users')

@login_required
@user_passes_test(is_admin)
def delete_instructor(request, instructor_id):
    """Vue pour supprimer un instructeur."""
    instructor = get_object_or_404(User, id=instructor_id, role='instructor')
    
    if request.method == 'POST':
        # Supprimer tous les cours associés à cet instructeur
        Course.objects.filter(instructor=instructor).delete()
        # Supprimer l'instructeur
        instructor.delete()
        messages.success(request, "L'instructeur et tous ses cours ont été supprimés avec succès.")
        return redirect('admin_instructors')
    
    return render(request, 'courses/admin_confirm_delete.html', {'object': instructor, 'title': "Supprimer l'instructeur"})









