{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .form-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .form-header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
  }

  .form-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.8;
  }

  .back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
  }

  .back-btn:hover {
    color: #5a67d8;
    transform: translateX(-5px);
  }

  .form-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2d3748;
  }

  .form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
  }

  .form-control:focus {
    outline: none;
    border-color: #C8A8E9;
    background: white;
    box-shadow: 0 0 0 3px rgba(200, 168, 233, 0.1);
  }

  textarea.form-control {
    min-height: 120px;
    resize: vertical;
  }

  select.form-control {
    cursor: pointer;
  }

  .btn-group {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
  }

  .btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  }

  .btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
  }

  .btn-secondary:hover {
    background: #cbd5e0;
    transform: translateY(-2px);
  }

  .required {
    color: #e53e3e;
  }

  .help-text {
    font-size: 0.875rem;
    color: #718096;
    margin-top: 0.25rem;
  }

  .error-list {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0 0;
  }

  .error-list li {
    color: #e53e3e;
    font-size: 0.875rem;
    padding: 0.25rem 0;
  }

  .content-type-info {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 0.5rem;
  }

  .content-type-info h6 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
    font-weight: 600;
  }

  .content-type-info ul {
    margin: 0;
    padding-left: 1.5rem;
    color: #718096;
  }

  .file-upload-area {
    border: 2px dashed #e2e8f0;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: #f8fafc;
  }

  .file-upload-area:hover {
    border-color: #C8A8E9;
    background: #f0f4ff;
  }

  .file-upload-area i {
    font-size: 2rem;
    color: #718096;
    margin-bottom: 1rem;
  }

  @media (max-width: 768px) {
    .form-container {
      padding: 1rem;
    }
    
    .form-header {
      padding: 1.5rem;
    }
    
    .form-card {
      padding: 1.5rem;
    }
    
    .btn-group {
      flex-direction: column;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
  <a href="{% url 'instructor_course_detail' course.id %}" class="back-btn">
    <i class="fas fa-arrow-left"></i> Retour au cours
  </a>

  <div class="form-header">
    <h1><i class="fas fa-play-circle"></i> {{ title }}</h1>
    <p>Module : {{ module.title }}</p>
    <p>Cours : {{ course.title }}</p>
  </div>

  <div class="form-card">
    <form method="post" enctype="multipart/form-data">
      {% csrf_token %}
      
      <div class="form-group">
        <label for="{{ form.title.id_for_label }}">
          {{ form.title.label }} <span class="required">*</span>
        </label>
        {{ form.title }}
        {% if form.title.errors %}
          <ul class="error-list">
            {% for error in form.title.errors %}
              <li>{{ error }}</li>
            {% endfor %}
          </ul>
        {% endif %}
        {% if form.title.help_text %}
          <div class="help-text">{{ form.title.help_text }}</div>
        {% endif %}
      </div>

      <div class="form-group">
        <label for="{{ form.content_type.id_for_label }}">
          {{ form.content_type.label }} <span class="required">*</span>
        </label>
        {{ form.content_type }}
        {% if form.content_type.errors %}
          <ul class="error-list">
            {% for error in form.content_type.errors %}
              <li>{{ error }}</li>
            {% endfor %}
          </ul>
        {% endif %}
        <div class="content-type-info">
          <h6>Types de contenu disponibles :</h6>
          <ul>
            <li><strong>Texte :</strong> Contenu textuel avec formatage</li>
            <li><strong>Vidéo :</strong> Fichier vidéo ou lien YouTube</li>
            <li><strong>Quiz :</strong> Questions interactives</li>
            <li><strong>Devoir :</strong> Assignation de travaux</li>
          </ul>
        </div>
      </div>

      <div class="form-group">
        <label for="{{ form.content.id_for_label }}">
          {{ form.content.label }}
        </label>
        {{ form.content }}
        {% if form.content.errors %}
          <ul class="error-list">
            {% for error in form.content.errors %}
              <li>{{ error }}</li>
            {% endfor %}
          </ul>
        {% endif %}
        {% if form.content.help_text %}
          <div class="help-text">{{ form.content.help_text }}</div>
        {% endif %}
      </div>

      {% if form.file_upload %}
      <div class="form-group">
        <label for="{{ form.file_upload.id_for_label }}">
          {{ form.file_upload.label }}
        </label>
        <div class="file-upload-area">
          <i class="fas fa-cloud-upload-alt"></i>
          <p>Cliquez pour sélectionner un fichier ou glissez-déposez</p>
          {{ form.file_upload }}
        </div>
        {% if form.file_upload.errors %}
          <ul class="error-list">
            {% for error in form.file_upload.errors %}
              <li>{{ error }}</li>
            {% endfor %}
          </ul>
        {% endif %}
        {% if form.file_upload.help_text %}
          <div class="help-text">{{ form.file_upload.help_text }}</div>
        {% endif %}
      </div>
      {% endif %}

      <div class="btn-group">
        <a href="{% url 'instructor_course_detail' course.id %}" class="btn btn-secondary">
          <i class="fas fa-times"></i> Annuler
        </a>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i> 
          {% if lesson %}Mettre à jour{% else %}Créer la leçon{% endif %}
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock %}
