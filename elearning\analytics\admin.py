from django.contrib import admin
from .models import UserStatistics, CourseStatistics, SystemStatistics

@admin.register(UserStatistics)
class UserStatisticsAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_courses_enrolled', 'total_courses_completed', 'total_lessons_completed', 'last_activity']
    search_fields = ['user__username']

@admin.register(CourseStatistics)
class CourseStatisticsAdmin(admin.ModelAdmin):
    list_display = ['course', 'total_enrollments', 'total_completions', 'average_completion_rate', 'average_rating']
    search_fields = ['course__title']

@admin.register(SystemStatistics)
class SystemStatisticsAdmin(admin.ModelAdmin):
    list_display = ['date', 'total_users', 'total_students', 'total_instructors', 'total_courses', 'daily_active_users']
    list_filter = ['date']
