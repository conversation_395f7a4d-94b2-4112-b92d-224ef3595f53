{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/admin_style.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  /* Styles directs pour s'assurer que les changements sont appliqués */
  .admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .admin-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 40px;
    margin-bottom: 2rem;
    border-radius: 24px;
    box-shadow: 0 15px 35px rgba(200, 168, 233, 0.3);
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .admin-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }

  .admin-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
  }

  .admin-header p {
    position: relative;
    z-index: 2;
    opacity: 0.9;
  }

  .admin-form {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 2rem;
  }

  .card-header.bg-primary {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0) !important;
    color: #333 !important;
    border: none;
  }

  .btn-accent {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 20px;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  .btn-accent:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    color: white;
  }

  /* Boutons d'action modernes */
  .btn-add-user {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 20px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    margin-bottom: 30px;
  }

  .btn-add-user:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
  }

  .btn-add-user i {
    margin-right: 10px;
  }

  /* Styles pour les boutons d'action dans le tableau */
  .btn-sm {
    padding: 8px 15px;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.3s;
  }

  .btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
  }

  .btn-outline-primary:hover {
    background: #667eea;
    border-color: #667eea;
    transform: translateY(-1px);
  }

  .btn-outline-danger {
    border-color: #e74c3c;
    color: #e74c3c;
  }

  .btn-outline-danger:hover {
    background: #e74c3c;
    border-color: #e74c3c;
    transform: translateY(-1px);
  }

  /* Modal moderne */
  .modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0) !important;
    color: #333 !important;
    border-radius: 20px 20px 0 0;
    border-bottom: none;
    padding: 25px 30px;
  }

  .modal-body {
    padding: 30px;
  }

  .modal-footer {
    border-top: none;
    padding: 20px 30px 30px;
  }

  /* Champs de formulaire dans la modal */
  .form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
  }

  .form-control, .form-select {
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: 12px;
    padding: 12px 16px;
    transition: all 0.3s;
  }

  .form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
</style>
{% endblock %}

{% block title %}Liste des utilisateurs{% endblock %}

{% block content %}
<div class="admin-container">
  <div class="admin-header">
    <h1><i class="fas fa-users"></i> Gestion des Utilisateurs</h1>
    <p>Gérez les comptes utilisateurs, modifiez les rôles et supprimez des utilisateurs si nécessaire.</p>
  </div>

  <!-- Barre de recherche et filtre -->
  <div class="admin-form">
    <form method="get" class="row g-3">
      <div class="col-md-5">
        <div class="input-group">
          <span class="input-group-text"><i class="fas fa-search"></i></span>
          <input type="text" name="q" class="form-control" placeholder="Rechercher par nom ou email" value="{{ request.GET.q }}">
        </div>
      </div>
      <div class="col-md-4">
        <div class="input-group">
          <span class="input-group-text"><i class="fas fa-filter"></i></span>
          <select name="role" class="form-select">
            <option value="">Tous les rôles</option>
            <option value="student" {% if request.GET.role == "student" %}selected{% endif %}>
              <i class="fas fa-user-graduate"></i> Étudiant
            </option>
            <option value="instructor" {% if request.GET.role == "instructor" %}selected{% endif %}>
              <i class="fas fa-chalkboard-teacher"></i> Instructeur
            </option>
          </select>
        </div>
      </div>
      <div class="col-md-3">
        <button type="submit" class="btn btn-primary w-100">
          <i class="fas fa-filter"></i> Filtrer
        </button>
      </div>
    </form>
  </div>

  <!-- Tableau -->
  <div class="card shadow-sm">
    <div class="card-header bg-primary text-white">
      <div class="row">
        <div class="col-md-3"><i class="fas fa-user"></i> Nom d'utilisateur</div>
        <div class="col-md-3"><i class="fas fa-envelope"></i> Email</div>
        <div class="col-md-2"><i class="fas fa-tag"></i> Rôle</div>
        <div class="col-md-2"><i class="fas fa-calendar-alt"></i> Date d'inscription</div>
        <div class="col-md-2 text-center"><i class="fas fa-cogs"></i> Actions</div>
      </div>
    </div>
    <div class="card-body p-0">
      {% for user in users %}
        <div class="p-3 border-bottom">
          <div class="row align-items-center">
            <div class="col-md-3">{{ user.username }}</div>
            <div class="col-md-3">{{ user.email }}</div>
            <div class="col-md-2">
              {% if user.role == 'instructor' %}
                <span class="badge bg-warning text-dark rounded-pill px-3 py-2">
                  <i class="fas fa-chalkboard-teacher"></i> Instructeur
                </span>
              {% elif user.role == 'student' %}
                <span class="badge bg-success rounded-pill px-3 py-2">
                  <i class="fas fa-user-graduate"></i> Étudiant
                </span>
              {% endif %}
            </div>
            <div class="col-md-2">{{ user.date_joined|date:"d/m/Y H:i" }}</div>
            <div class="col-md-2 text-center">
              <a href="{% url 'delete_user' user.id %}" class="btn btn-danger btn-sm">
                <i class="fas fa-trash-alt"></i> Supprimer
              </a>
            </div>
          </div>
        </div>
      {% empty %}
        <div class="p-5 text-center">
          <i class="fas fa-users fa-3x text-muted mb-3"></i>
          <h5>Aucun utilisateur trouvé</h5>
          <p class="text-muted">Ajoutez un nouvel utilisateur pour commencer</p>
          <button type="button" class="btn btn-accent mt-2" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-1"></i> AJOUTER UN UTILISATEUR
          </button>
        </div>
      {% endfor %}
    </div>
  </div>

  <!-- Pagination -->
  {% if users.has_other_pages %}
  <nav class="mt-4">
    <ul class="pagination justify-content-center">
      {% if users.has_previous %}
        <li class="page-item">
          <a class="page-link" href="?page={{ users.previous_page_number }}&q={{ request.GET.q }}&role={{ request.GET.role }}">
            <i class="fas fa-chevron-left"></i> Précédent
          </a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <span class="page-link"><i class="fas fa-chevron-left"></i> Précédent</span>
        </li>
      {% endif %}

      {% for i in users.paginator.page_range %}
        <li class="page-item {% if users.number == i %}active{% endif %}">
          <a class="page-link" href="?page={{ i }}&q={{ request.GET.q }}&role={{ request.GET.role }}">{{ i }}</a>
        </li>
      {% endfor %}

      {% if users.has_next %}
        <li class="page-item">
          <a class="page-link" href="?page={{ users.next_page_number }}&q={{ request.GET.q }}&role={{ request.GET.role }}">
            Suivant <i class="fas fa-chevron-right"></i>
          </a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <span class="page-link">Suivant <i class="fas fa-chevron-right"></i></span>
        </li>
      {% endif %}
    </ul>
  </nav>
  {% endif %}

  <!-- Bouton d'ajout flottant -->
  <div class="position-fixed bottom-0 end-0 p-4">
    <button type="button" class="btn btn-accent btn-lg rounded-circle shadow" style="width: 60px; height: 60px;" data-bs-toggle="modal" data-bs-target="#addUserModal">
      <i class="fas fa-plus"></i>
    </button>
  </div>

  <!-- Modal d'ajout d'utilisateur -->
  <div class="modal fade" id="addUserModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-user-plus me-2"></i>Ajouter un nouvel utilisateur
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form method="POST" action="{% url 'create_user' %}">
          {% csrf_token %}
          <div class="modal-body">
            <div class="mb-4">
              <label for="username" class="form-label">
                <i class="fas fa-user me-2"></i>Nom d'utilisateur*
              </label>
              <input type="text" class="form-control" id="username" name="username" required placeholder="Ex: jean.dupont">
            </div>
            <div class="mb-4">
              <label for="email" class="form-label">
                <i class="fas fa-envelope me-2"></i>Email*
              </label>
              <input type="email" class="form-control" id="email" name="email" required placeholder="<EMAIL>">
            </div>
            <div class="mb-4">
              <label for="password" class="form-label">
                <i class="fas fa-lock me-2"></i>Mot de passe*
              </label>
              <input type="password" class="form-control" id="password" name="password" required placeholder="Minimum 8 caractères">
              <small class="form-text text-muted">Minimum 8 caractères, pas trop simple</small>
            </div>
            <!-- Le rôle est automatiquement défini comme étudiant -->
            <input type="hidden" name="role" value="student">
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i>Annuler
            </button>
            <button type="submit" class="btn btn-accent">
              <i class="fas fa-user-plus me-1"></i>Créer l'utilisateur
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

