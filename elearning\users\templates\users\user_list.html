{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/admin_style.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  /* Styles directs pour s'assurer que les changements sont appliqués */
  .admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .admin-header {
    background: linear-gradient(135deg, #4361ee, #3a0ca3);
    color: white;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .admin-header h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
  }

  .admin-form {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 2rem;
  }

  .card-header.bg-primary {
    background-color: #4361ee !important;
  }

  .btn-accent {
    background-color: #f72585;
    border-color: #f72585;
    color: white;
  }

  .btn-accent:hover {
    background-color: #d61c69;
    border-color: #d61c69;
  }
</style>
{% endblock %}

{% block title %}Liste des utilisateurs{% endblock %}

{% block content %}
<div class="admin-container">
  <div class="admin-header">
    <h1><i class="fas fa-users"></i> Gestion des Utilisateurs</h1>
    <p>Gérez les comptes utilisateurs, modifiez les rôles et supprimez des utilisateurs si nécessaire.</p>
  </div>

  <!-- Barre de recherche et filtre -->
  <div class="admin-form">
    <form method="get" class="row g-3">
      <div class="col-md-5">
        <div class="input-group">
          <span class="input-group-text"><i class="fas fa-search"></i></span>
          <input type="text" name="q" class="form-control" placeholder="Rechercher par nom ou email" value="{{ request.GET.q }}">
        </div>
      </div>
      <div class="col-md-4">
        <div class="input-group">
          <span class="input-group-text"><i class="fas fa-filter"></i></span>
          <select name="role" class="form-select">
            <option value="">Tous les rôles</option>
            <option value="student" {% if request.GET.role == "student" %}selected{% endif %}>
              <i class="fas fa-user-graduate"></i> Étudiant
            </option>
            <option value="instructor" {% if request.GET.role == "instructor" %}selected{% endif %}>
              <i class="fas fa-chalkboard-teacher"></i> Instructeur
            </option>
          </select>
        </div>
      </div>
      <div class="col-md-3">
        <button type="submit" class="btn btn-primary w-100">
          <i class="fas fa-filter"></i> Filtrer
        </button>
      </div>
    </form>
  </div>

  <!-- Tableau -->
  <div class="card shadow-sm">
    <div class="card-header bg-primary text-white">
      <div class="row">
        <div class="col-md-3"><i class="fas fa-user"></i> Nom d'utilisateur</div>
        <div class="col-md-3"><i class="fas fa-envelope"></i> Email</div>
        <div class="col-md-2"><i class="fas fa-tag"></i> Rôle</div>
        <div class="col-md-2"><i class="fas fa-calendar-alt"></i> Date d'inscription</div>
        <div class="col-md-2 text-center"><i class="fas fa-cogs"></i> Actions</div>
      </div>
    </div>
    <div class="card-body p-0">
      {% for user in users %}
        <div class="p-3 border-bottom">
          <div class="row align-items-center">
            <div class="col-md-3">{{ user.username }}</div>
            <div class="col-md-3">{{ user.email }}</div>
            <div class="col-md-2">
              {% if user.role == 'instructor' %}
                <span class="badge bg-warning text-dark rounded-pill px-3 py-2">
                  <i class="fas fa-chalkboard-teacher"></i> Instructeur
                </span>
              {% elif user.role == 'student' %}
                <span class="badge bg-success rounded-pill px-3 py-2">
                  <i class="fas fa-user-graduate"></i> Étudiant
                </span>
              {% endif %}
            </div>
            <div class="col-md-2">{{ user.date_joined|date:"d/m/Y H:i" }}</div>
            <div class="col-md-2 text-center">
              <a href="{% url 'delete_user' user.id %}" class="btn btn-danger btn-sm">
                <i class="fas fa-trash-alt"></i> Supprimer
              </a>
            </div>
          </div>
        </div>
      {% empty %}
        <div class="p-5 text-center">
          <i class="fas fa-users fa-3x text-muted mb-3"></i>
          <h5>Aucun utilisateur trouvé</h5>
          <p class="text-muted">Ajoutez un nouvel utilisateur pour commencer</p>
          <button type="button" class="btn btn-accent mt-2" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-1"></i> AJOUTER UN UTILISATEUR
          </button>
        </div>
      {% endfor %}
    </div>
  </div>

  <!-- Pagination -->
  {% if users.has_other_pages %}
  <nav class="mt-4">
    <ul class="pagination justify-content-center">
      {% if users.has_previous %}
        <li class="page-item">
          <a class="page-link" href="?page={{ users.previous_page_number }}&q={{ request.GET.q }}&role={{ request.GET.role }}">
            <i class="fas fa-chevron-left"></i> Précédent
          </a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <span class="page-link"><i class="fas fa-chevron-left"></i> Précédent</span>
        </li>
      {% endif %}

      {% for i in users.paginator.page_range %}
        <li class="page-item {% if users.number == i %}active{% endif %}">
          <a class="page-link" href="?page={{ i }}&q={{ request.GET.q }}&role={{ request.GET.role }}">{{ i }}</a>
        </li>
      {% endfor %}

      {% if users.has_next %}
        <li class="page-item">
          <a class="page-link" href="?page={{ users.next_page_number }}&q={{ request.GET.q }}&role={{ request.GET.role }}">
            Suivant <i class="fas fa-chevron-right"></i>
          </a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <span class="page-link">Suivant <i class="fas fa-chevron-right"></i></span>
        </li>
      {% endif %}
    </ul>
  </nav>
  {% endif %}

  <!-- Bouton d'ajout flottant -->
  <div class="position-fixed bottom-0 end-0 p-4">
    <button type="button" class="btn btn-accent btn-lg rounded-circle shadow" style="width: 60px; height: 60px;" data-bs-toggle="modal" data-bs-target="#addUserModal">
      <i class="fas fa-plus"></i>
    </button>
  </div>

  <!-- Modal d'ajout d'utilisateur -->
  <div class="modal fade" id="addUserModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">
            <i class="fas fa-plus-circle me-2"></i>Ajouter un nouvel utilisateur
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form method="POST" action="{% url 'create_user' %}">
          {% csrf_token %}
          <div class="modal-body">
            <div class="mb-3">
              <label for="username" class="form-label">Nom d'utilisateur</label>
              <input type="text" class="form-control" id="username" name="username" required>
            </div>
            <div class="mb-3">
              <label for="email" class="form-label">Email</label>
              <input type="email" class="form-control" id="email" name="email" required>
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">Mot de passe</label>
              <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <div class="mb-3">
              <label for="role" class="form-label">Rôle</label>
              <select class="form-select" id="role" name="role" required>
                <option value="">Sélectionner un rôle</option>
                <option value="student">Étudiant</option>
                <option value="instructor">Instructeur</option>
              </select>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="submit" class="btn btn-accent">
              <i class="fas fa-save me-1"></i> AJOUTER
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

