#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserProfile

def create_instructor():
    # Créer un utilisateur instructeur
    username = 'instructor1'
    email = '<EMAIL>'
    password = 'password123'
    
    # Vérifier si l'utilisateur existe déjà
    if User.objects.filter(username=username).exists():
        print(f"L'utilisateur {username} existe déjà")
        user = User.objects.get(username=username)
    else:
        # Créer l'utilisateur
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name='<PERSON>',
            last_name='Du<PERSON>'
        )
        print(f"Utilisateur {username} créé avec succès")
    
    # <PERSON><PERSON><PERSON> ou mettre à jour le profil
    profile, created = UserProfile.objects.get_or_create(
        user=user,
        defaults={'role': 'instructor'}
    )
    
    if not created:
        profile.role = 'instructor'
        profile.save()
        print(f"Profil mis à jour pour {username}")
    else:
        print(f"Profil instructeur créé pour {username}")
    
    print(f"\nInformations de connexion:")
    print(f"Username: {username}")
    print(f"Password: {password}")
    print(f"Role: {profile.role}")

if __name__ == '__main__':
    create_instructor()
