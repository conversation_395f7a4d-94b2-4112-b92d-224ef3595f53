{% extends 'base.html' %}

{% block title %}{{ title }} - Administration{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <!-- Header avec navigation -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="text-gradient mb-0">{{ title }}</h2>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard Admin</a></li>
          <li class="breadcrumb-item"><a href="{% url 'admin_courses' %}">Gestion des Cours</a></li>
          <li class="breadcrumb-item active">Supprimer</li>
        </ol>
      </nav>
    </div>
  </div>

  <!-- Carte de confirmation -->
  <div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
      <div class="card shadow-lg border-0">
        <div class="card-header text-white" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
          <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle me-3" style="font-size: 1.5rem;"></i>
            <div>
              <h5 class="mb-0">Confirmation de suppression</h5>
              <small class="opacity-75">Action irréversible</small>
            </div>
          </div>
        </div>
        
        <div class="card-body p-4">
          <!-- Informations du cours -->
          <div class="alert alert-warning border-0 mb-4" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
            <div class="d-flex align-items-start">
              <i class="fas fa-book text-warning me-3 mt-1"></i>
              <div>
                <h6 class="alert-heading mb-2">Cours à supprimer</h6>
                <p class="mb-1"><strong>{{ object.title }}</strong></p>
                <small class="text-muted">
                  Instructeur: {{ object.instructor.username }} | 
                  Étudiants inscrits: {{ object.students.count }} |
                  Créé le: {{ object.created_at|date:"d/m/Y" }}
                </small>
              </div>
            </div>
          </div>

          <!-- Avertissement -->
          <div class="alert alert-danger border-0 mb-4" style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
            <div class="d-flex align-items-start">
              <i class="fas fa-exclamation-circle text-danger me-3 mt-1"></i>
              <div>
                <h6 class="alert-heading text-danger mb-2">Attention !</h6>
                <p class="mb-2">Cette action est <strong>irréversible</strong> et supprimera :</p>
                <ul class="mb-0 small">
                  <li>Le cours et toutes ses informations</li>
                  <li>Tous les modules et leçons associés</li>
                  <li>Tous les quiz liés au cours</li>
                  <li>L'historique des inscriptions des étudiants</li>
                  <li>Les statistiques et données de progression</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Formulaire de confirmation -->
          <form method="post" class="text-center">
            {% csrf_token %}
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
              <button type="submit" class="btn btn-danger btn-lg px-4 me-md-2">
                <i class="fas fa-trash-alt me-2"></i>
                Confirmer la suppression
              </button>
              <a href="{% url 'admin_courses' %}" class="btn btn-secondary btn-lg px-4">
                <i class="fas fa-times me-2"></i>
                Annuler
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.text-gradient {
  background: linear-gradient(135deg, #C8A8E9 0%, #A8D0F0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card {
  border-radius: 15px;
  overflow: hidden;
}

.card-header {
  border: none;
  padding: 1.5rem;
}

.btn {
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

.alert {
  border-radius: 10px;
}

.breadcrumb {
  background: none;
  padding: 0;
  margin: 0;
}

.breadcrumb-item a {
  color: #6c757d;
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: #C8A8E9;
}
</style>
{% endblock %}
