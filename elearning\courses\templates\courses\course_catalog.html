{% extends 'base.html' %}
{% load static %}

{% block title %}Catalogue des Cours{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .catalog-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .catalog-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 3rem 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .filters-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .search-bar {
    position: relative;
    margin-bottom: 1.5rem;
  }

  .search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 50px;
    font-size: 1rem;
    transition: all 0.3s ease;
  }

  .search-input:focus {
    border-color: #C8A8E9;
    box-shadow: 0 0 0 0.2rem rgba(200, 168, 233, 0.25);
    outline: none;
  }

  .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
  }

  .filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
  }

  .filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
  }

  .filter-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    transition: all 0.3s ease;
  }

  .filter-select:focus {
    border-color: #C8A8E9;
    box-shadow: 0 0 0 0.2rem rgba(200, 168, 233, 0.25);
    outline: none;
  }

  .filter-btn {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    height: fit-content;
  }

  .filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  }

  .courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .course-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
  }

  .course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  }

  .course-image {
    height: 200px;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    position: relative;
  }

  .course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .course-level {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
  }

  .level-beginner { background: rgba(46, 204, 113, 0.9); }
  .level-intermediate { background: rgba(248, 150, 30, 0.9); }
  .level-advanced { background: rgba(249, 65, 68, 0.9); }

  .course-content {
    padding: 1.5rem;
  }

  .course-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.75rem;
    line-height: 1.3;
  }

  .course-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
  }

  .course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.8rem;
    color: #777;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .course-actions {
    display: flex;
    gap: 0.5rem;
  }

  .btn-view {
    flex: 1;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    text-decoration: none;
    text-align: center;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-view:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
  }

  .btn-enroll {
    flex: 1;
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    text-decoration: none;
    text-align: center;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-enroll:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
  }

  .btn-enrolled {
    flex: 1;
    background: #28a745;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    text-decoration: none;
    text-align: center;
    font-weight: 600;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #C8A8E9;
  }

  .results-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    text-align: center;
    color: #666;
  }
</style>
{% endblock %}

{% block content %}
<div class="catalog-container">
  <div class="catalog-header">
    <h1><i class="fas fa-graduation-cap"></i> Catalogue des Cours</h1>
    <p>Découvrez nos cours et développez vos compétences</p>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section">
    <form method="get" action="{% url 'course_catalog' %}">
      <div class="search-bar">
        <i class="fas fa-search search-icon"></i>
        <input type="text" name="search" class="search-input" 
               placeholder="Rechercher un cours..." 
               value="{{ current_search|default:'' }}">
      </div>
      
      <div class="filter-row">
        <div class="filter-group">
          <label for="category">Catégorie</label>
          <select name="category" id="category" class="filter-select">
            <option value="">Toutes les catégories</option>
            {% for value, label in category_choices %}
            <option value="{{ value }}" {% if current_category == value %}selected{% endif %}>
              {{ label }}
            </option>
            {% endfor %}
          </select>
        </div>

        <div class="filter-group">
          <label for="level">Niveau</label>
          <select name="level" id="level" class="filter-select">
            <option value="">Tous les niveaux</option>
            {% for value, label in level_choices %}
            <option value="{{ value }}" {% if current_level == value %}selected{% endif %}>
              {{ label }}
            </option>
            {% endfor %}
          </select>
        </div>

        <div class="filter-group">
          <button type="submit" class="filter-btn">
            <i class="fas fa-filter"></i> Filtrer
          </button>
        </div>
      </div>
    </form>
  </div>

  <!-- Informations sur les résultats -->
  {% if page_obj %}
  <div class="results-info">
    <i class="fas fa-info-circle"></i>
    {{ page_obj.paginator.count }} cours trouvé{{ page_obj.paginator.count|pluralize }}
    {% if current_search or current_category or current_level %}
    - Filtres actifs
    {% endif %}
  </div>
  {% endif %}

  <!-- Grille des cours -->
  {% if page_obj %}
  <div class="courses-grid">
    {% for course in page_obj %}
    <div class="course-card">
      <div class="course-image">
        {% if course.image %}
          <img src="{{ course.image.url }}" alt="{{ course.title }}">
        {% else %}
          <i class="fas fa-graduation-cap"></i>
        {% endif %}
        <div class="course-level level-{{ course.level }}">
          {{ course.get_level_display }}
        </div>
      </div>
      
      <div class="course-content">
        <h3 class="course-title">{{ course.title }}</h3>
        <p class="course-description">{{ course.description|truncatewords:15 }}</p>
        
        <div class="course-meta">
          <div class="meta-item">
            <i class="fas fa-users"></i>
            <span>{{ course.student_count }} étudiants</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-book"></i>
            <span>{{ course.lesson_count }} leçons</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-tag"></i>
            <span>{{ course.get_category_display }}</span>
          </div>
        </div>
        
        <div class="course-actions">
          <a href="{% url 'course_detail_public' course.id %}" class="btn-view">
            <i class="fas fa-eye"></i> Voir
          </a>
          {% if user.is_authenticated %}
            {% if course.is_enrolled %}
              <a href="{% url 'course_learn' course.id %}" class="btn-enrolled">
                <i class="fas fa-play"></i> Continuer
              </a>
            {% else %}
              <a href="{% url 'enroll_course' course.id %}" class="btn-enroll">
                <i class="fas fa-plus"></i> S'inscrire
              </a>
            {% endif %}
          {% else %}
            <a href="{% url 'login' %}" class="btn-enroll">
              <i class="fas fa-sign-in-alt"></i> Connexion
            </a>
          {% endif %}
        </div>
      </div>
    </div>
    {% endfor %}
  </div>

  {% else %}
  <div class="empty-state">
    <div class="empty-icon">
      <i class="fas fa-search"></i>
    </div>
    <h3>Aucun cours trouvé</h3>
    <p>Essayez de modifier vos critères de recherche</p>
    <a href="{% url 'course_catalog' %}" class="filter-btn">
      <i class="fas fa-refresh"></i> Réinitialiser les filtres
    </a>
  </div>
  {% endif %}
</div>
{% endblock %}
