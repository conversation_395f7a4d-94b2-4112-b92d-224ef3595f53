{% extends 'base.html' %}
{% load static %}

{% block title %}Découvrez nos cours | E-Learn+{% endblock %}

{% block content %}
<div class="course-catalog-header bg-primary text-white py-5">
  <div class="container text-center">
    <h1 class="display-4">Découvrez nos cours</h1>
    <p class="lead">Explorez notre catalogue de cours et commencez votre parcours d'apprentissage dès aujourd'hui</p>
    
    <!-- Barre de recherche -->
    <div class="row justify-content-center mt-4">
      <div class="col-md-8">
        <form action="{% url 'search_courses' %}" method="GET" class="d-flex">
          <input type="text" name="q" class="form-control form-control-lg" placeholder="Rechercher un cours..." aria-label="Rechercher">
          <button class="btn btn-light ms-2" type="submit">
            <i class="fas fa-search"></i> Rechercher
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Filtres de catégories -->
<div class="container mt-4">
  <div class="category-filters text-center mb-4">
    <a href="{% url 'course_catalog' %}" class="btn btn-primary me-2 {% if not category %}active{% endif %}">Tous</a>
    <a href="{% url 'course_catalog' %}?category=programming" class="btn btn-outline-primary me-2 {% if category == 'programming' %}active{% endif %}">Programmation</a>
    <a href="{% url 'course_catalog' %}?category=design" class="btn btn-outline-primary me-2 {% if category == 'design' %}active{% endif %}">Design</a>
    <a href="{% url 'course_catalog' %}?category=business" class="btn btn-outline-primary me-2 {% if category == 'business' %}active{% endif %}">Business</a>
    <a href="{% url 'course_catalog' %}?category=marketing" class="btn btn-outline-primary me-2 {% if category == 'marketing' %}active{% endif %}">Marketing</a>
    <a href="{% url 'course_catalog' %}?category=personal" class="btn btn-outline-primary {% if category == 'personal' %}active{% endif %}">Développement personnel</a>
  </div>
  
  <!-- Liste des cours -->
  <div class="row">
    {% for course in courses %}
    <div class="col-md-4 mb-4">
      <div class="card h-100 shadow-sm">
        {% if course.image %}
        <img src="{{ course.image.url }}" class="card-img-top" alt="{{ course.title }}">
        {% else %}
        <div class="card-img-top bg-light text-center py-5">
          <i class="fas fa-book fa-3x text-muted"></i>
        </div>
        {% endif %}
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <span class="badge bg-primary">{{ course.get_category_display }}</span>
            <div class="course-rating">
              <i class="fas fa-star text-warning"></i>
              <span>{{ course.rating }}</span>
            </div>
          </div>
          <h5 class="card-title">{{ course.title }}</h5>
          <p class="card-text text-muted">{{ course.description|truncatewords:15 }}</p>
        </div>
        <div class="card-footer bg-white d-flex justify-content-between align-items-center">
          <small class="text-muted">
            <i class="fas fa-clock me-1"></i> {{ course.duration }}
          </small>
          <a href="{% url 'course_detail' course.id %}" class="btn btn-sm btn-outline-primary">Voir le cours</a>
        </div>
      </div>
    </div>
    {% empty %}
    <div class="col-12 text-center py-5">
      <i class="fas fa-search fa-3x text-muted mb-3"></i>
      <h3>Aucun cours trouvé</h3>
      <p class="text-muted">Essayez de modifier vos critères de recherche</p>
    </div>
    {% endfor %}
  </div>
  
  <!-- Pagination -->
  {% if courses.has_other_pages %}
  <nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center">
      {% if courses.has_previous %}
      <li class="page-item">
        <a class="page-link" href="?page={{ courses.previous_page_number }}{% if category %}&category={{ category }}{% endif %}" aria-label="Previous">
          <span aria-hidden="true">&laquo;</span>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">&laquo;</a>
      </li>
      {% endif %}
      
      {% for i in courses.paginator.page_range %}
        {% if courses.number == i %}
        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
        {% else %}
        <li class="page-item"><a class="page-link" href="?page={{ i }}{% if category %}&category={{ category }}{% endif %}">{{ i }}</a></li>
        {% endif %}
      {% endfor %}
      
      {% if courses.has_next %}
      <li class="page-item">
        <a class="page-link" href="?page={{ courses.next_page_number }}{% if category %}&category={{ category }}{% endif %}" aria-label="Next">
          <span aria-hidden="true">&raquo;</span>
        </a>
      </li>
      {% else %}
      <li class="page-item disabled">
        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">&raquo;</a>
      </li>
      {% endif %}
    </ul>
  </nav>
  {% endif %}
</div>
{% endblock %}