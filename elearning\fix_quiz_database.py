#!/usr/bin/env python
"""
Script pour corriger la base de données des quiz
"""
import os
import sys
import django
import sqlite3

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

from django.conf import settings

def main():
    print("🔧 CORRECTION DE LA BASE DE DONNÉES QUIZ")
    print("=" * 50)
    
    # Chemin vers la base de données SQLite
    db_path = settings.DATABASES['default']['NAME']
    print(f"Base de données: {db_path}")
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Supprimer toutes les tables des quiz si elles existent
        tables_to_drop = [
            'quizzes_quiz',
            'quizzes_question', 
            'quizzes_quizsubmission',
            'quizzes_quizanswer'
        ]
        
        for table in tables_to_drop:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"✅ Table {table} supprimée")
            except Exception as e:
                print(f"⚠️ Erreur lors de la suppression de {table}: {e}")
        
        # Supprimer l'entrée de migration des quiz
        try:
            cursor.execute("DELETE FROM django_migrations WHERE app = 'quizzes'")
            print("✅ Entrées de migration des quiz supprimées")
        except Exception as e:
            print(f"⚠️ Erreur lors de la suppression des migrations: {e}")
        
        conn.commit()
        conn.close()
        
        print("\n🎯 RECRÉATION DES MIGRATIONS")
        
        # Supprimer les fichiers de migration
        import shutil
        migration_dir = "quizzes/migrations"
        pycache_dir = f"{migration_dir}/__pycache__"
        
        if os.path.exists(pycache_dir):
            shutil.rmtree(pycache_dir)
            print("✅ Cache des migrations supprimé")
        
        # Supprimer les fichiers de migration (sauf __init__.py)
        for file in os.listdir(migration_dir):
            if file.endswith('.py') and file != '__init__.py':
                os.remove(os.path.join(migration_dir, file))
                print(f"✅ Migration {file} supprimée")
        
        print("\n🚀 PROCHAINES ÉTAPES:")
        print("1. Exécutez: python manage.py makemigrations quizzes")
        print("2. Exécutez: python manage.py migrate quizzes")
        print("3. Exécutez: python create_test_quizzes.py")
        print("4. Testez la page des quiz")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == '__main__':
    main()
