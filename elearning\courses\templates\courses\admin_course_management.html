{% extends 'base.html' %}
{% load static %}

{% block title %}Gestion des Cours | E-Learn+{% endblock %}

{% block content %}
<div class="container-fluid p-0">
  <!-- Navbar supérieure -->
  <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #4e54c8;">
    <div class="container-fluid">
      <a class="navbar-brand" href="{% url 'admin_dashboard' %}">
        <i class="fas fa-graduation-cap me-2"></i> E-Learn+
      </a>
      <div class="collapse navbar-collapse">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="{% url 'admin_dashboard' %}">
              <i class="fas fa-home me-1"></i> Accueil
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'admin_courses' %}">
              <i class="fas fa-book me-1"></i> Cours
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{% url 'admin_dashboard' %}">
              <i class="fas fa-cog me-1"></i> Administration
            </a>
          </li>
        </ul>
        <a href="{% url 'logout' %}" class="btn btn-light btn-sm rounded-pill px-3">
          <i class="fas fa-sign-out-alt me-1"></i> DÉCONNEXION
        </a>
      </div>
    </div>
  </nav>

  <!-- En-tête avec titre -->
  <div class="p-4 rounded-3 mb-4" style="background-color: #4e54c8; color: white;">
    <div class="d-flex align-items-center">
      <i class="fas fa-book-open fa-2x me-3"></i>
      <div>
        <h1 class="mb-0">Gestion des Cours</h1>
        <p class="mb-0">Gérez les cours, modifiez le contenu et supprimez des cours si nécessaire.</p>
      </div>
    </div>
  </div>

  <!-- Barre de recherche et filtres -->
  <div class="bg-white p-3 rounded-3 shadow-sm mb-4">
    <div class="row g-2 align-items-center">
      <div class="col-md-6">
        <div class="input-group">
          <span class="input-group-text bg-white border-end-0">
            <i class="fas fa-search text-muted"></i>
          </span>
          <input type="text" class="form-control border-start-0" placeholder="Rechercher par nom ou email" id="searchInput">
        </div>
      </div>
      <div class="col-md-4">
        <select class="form-select" id="roleFilter">
          <option value="">Tous les niveaux</option>
          <option value="Débutant">Débutant</option>
          <option value="Intermédiaire">Intermédiaire</option>
          <option value="Avancé">Avancé</option>
        </select>
      </div>
      <div class="col-md-2">
        <button class="btn w-100 text-white" style="background-color: #4e54c8;">
          <i class="fas fa-filter me-1"></i> Filtrer
        </button>
      </div>
    </div>
  </div>

  <!-- Tableau des cours -->
  <div class="bg-white rounded-3 shadow-sm">
    <!-- En-tête du tableau -->
    <div class="p-3 text-white rounded-top" style="background-color: #4e54c8;">
      <div class="row align-items-center">
        <div class="col-md-3">
          <i class="fas fa-book me-2"></i> Nom du cours
        </div>
        <div class="col-md-3 text-center">
          <i class="fas fa-envelope me-2"></i> Instructeur
        </div>
        <div class="col-md-2 text-center">
          <i class="fas fa-user-tag me-2"></i> Niveau
        </div>
        <div class="col-md-2 text-center">
          <i class="fas fa-calendar me-2"></i> Date de création
        </div>
        <div class="col-md-2 text-center">
          <i class="fas fa-cog me-2"></i> Actions
        </div>
      </div>
    </div>

    <!-- Corps du tableau -->
    <div>
      {% for course in courses %}
      <div class="p-3 border-bottom course-item">
        <div class="row align-items-center">
          <div class="col-md-3">
            <strong>{{ course.title }}</strong>
          </div>
          <div class="col-md-3 text-center">
            {{ course.instructor.email }}
          </div>
          <div class="col-md-2 text-center">
            <span class="badge rounded-pill {% if course.level == 'beginner' %}bg-success{% elif course.level == 'intermediate' %}bg-warning text-dark{% else %}bg-danger{% endif %} px-3 py-2">
              {% if course.level == 'beginner' %}
                <i class="fas fa-seedling me-1"></i> Débutant
              {% elif course.level == 'intermediate' %}
                <i class="fas fa-user me-1"></i> Intermédiaire
              {% else %}
                <i class="fas fa-star me-1"></i> Avancé
              {% endif %}
            </span>
          </div>
          <div class="col-md-2 text-center">
            {{ course.created_at|date:"d/m/Y H:i" }}
          </div>
          <div class="col-md-2 text-center">
            <button class="btn btn-sm text-white" style="background-color: #e74c3c; border: none;">
              <i class="fas fa-trash-alt"></i> Supprimer
            </button>
          </div>
        </div>
      </div>
      {% empty %}
      <div class="p-5 text-center">
        <i class="fas fa-book fa-3x text-muted mb-3"></i>
        <h5>Aucun cours trouvé</h5>
        <p class="text-muted">Ajoutez un nouveau cours pour commencer</p>
        <button type="button" class="btn text-white mt-2" style="background-color: #ff3366; border: none;" data-bs-toggle="modal" data-bs-target="#addCourseModal">
          <i class="fas fa-plus me-1"></i> AJOUTER UN COURS
        </button>
      </div>
      {% endfor %}
    </div>
  </div>

  <!-- Bouton d'ajout flottant -->
  <div class="position-fixed bottom-0 end-0 p-4">
    <button type="button" class="btn btn-lg rounded-circle shadow" style="background-color: #ff3366; color: white; width: 60px; height: 60px;" data-bs-toggle="modal" data-bs-target="#addCourseModal">
      <i class="fas fa-plus"></i>
    </button>
  </div>
</div>

<!-- Modal d'ajout de cours -->
<div class="modal fade" id="addCourseModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header" style="background-color: #4e54c8; color: white;">
        <h5 class="modal-title">
          <i class="fas fa-plus-circle me-2"></i>Ajouter un nouveau cours
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="POST" action="{% url 'create_course' %}" enctype="multipart/form-data">
        {% csrf_token %}
        <div class="modal-body">
          <div class="mb-3">
            <label for="title" class="form-label">Titre du cours</label>
            <input type="text" class="form-control" id="title" name="title" required>
          </div>
          <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="category" class="form-label">Catégorie</label>
                <select class="form-select" id="category" name="category" required>
                  <option value="">Sélectionner une catégorie</option>
                  <option value="programming">Programmation</option>
                  <option value="design">Design</option>
                  <option value="business">Business</option>
                  <option value="marketing">Marketing</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="level" class="form-label">Niveau</label>
                <select class="form-select" id="level" name="level" required>
                  <option value="">Sélectionner un niveau</option>
                  <option value="beginner">Débutant</option>
                  <option value="intermediate">Intermédiaire</option>
                  <option value="advanced">Avancé</option>
                </select>
              </div>
            </div>
          </div>
          <div class="mb-3">
            <label for="image" class="form-label">Image du cours</label>
            <input type="file" class="form-control" id="image" name="image">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
          <button type="submit" class="btn text-white" style="background-color: #ff3366; border: none;">
            <i class="fas fa-save me-1"></i> AJOUTER
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Script pour la recherche et le filtrage -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const roleFilter = document.getElementById('roleFilter');
    const courseItems = document.querySelectorAll('.course-item');
    
    function filterCourses() {
      const searchTerm = searchInput.value.toLowerCase();
      const selectedRole = roleFilter.value;
      
      courseItems.forEach(item => {
        const courseName = item.querySelector('.col-md-3 strong').textContent.toLowerCase();
        const courseRole = item.querySelector('.badge').textContent.trim();
        
        const matchesSearch = courseName.includes(searchTerm);
        const matchesRole = selectedRole === '' || courseRole.includes(selectedRole);
        
        if (matchesSearch && matchesRole) {
          item.style.display = '';
        } else {
          item.style.display = 'none';
        }
      });
    }
    
    searchInput.addEventListener('input', filterCourses);
    roleFilter.addEventListener('change', filterCourses);
  });
  
  function confirmDelete(courseId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce cours ? Cette action est irréversible.')) {
      window.location.href = `/courses/delete/${courseId}/`;
    }
  }
</script>
{% endblock %}


