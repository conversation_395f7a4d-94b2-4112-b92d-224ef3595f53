<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}E-Learn+{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Notre CSS personnalisé - Utilisons un style inline pour tester -->
    <style>
        /* Refonte moderne avec palette de couleurs vives */
        :root {
          --primary: #4361ee;
          --secondary: #3a0ca3;
          --success: #2ecc71;
          --info: #4cc9f0;
          --warning: #f8961e;
          --danger: #f94144;
          --light: #f8f9fa;
          --dark: #212529;
          --white: #ffffff;
          --gradient-primary: linear-gradient(135deg, #4361ee, #3a0ca3);
          --gradient-secondary: linear-gradient(135deg, #f72585, #b5179e);
          --gradient-success: linear-gradient(135deg, #2ecc71, #27ae60);
          --gradient-info: linear-gradient(135deg, #4cc9f0, #3498db);
          --gradient-warning: linear-gradient(135deg, #f8961e, #f39c12);
          --gradient-danger: linear-gradient(135deg, #f94144, #e74c3c);
          --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
          --border-radius: 12px;
          --border-radius-lg: 16px;
          --border-radius-sm: 8px;
          --transition: all 0.3s ease;
        }

        body {
          background-color: #f5f7fa;
          font-family: 'Poppins', sans-serif;
        }

        /* Barre de navigation colorée et moderne */
        .navbar {
          background: linear-gradient(135deg, #4361ee, #3a0ca3) !important;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          padding: 15px 0;
        }

        .navbar-brand {
          font-weight: 700;
          color: white !important;
        }

        .navbar .nav-link {
          color: rgba(255, 255, 255, 0.9) !important;
          font-weight: 600;
          transition: all 0.3s ease;
          padding: 8px 15px;
          border-radius: 50px;
        }

        .navbar .nav-link:hover {
          background: rgba(255, 255, 255, 0.15);
          transform: translateY(-2px);
        }

        /* Bouton d'ajout de cours plus visible */
        .btn-add-course {
          background: linear-gradient(135deg, #f72585, #b5179e);
          color: white !important;
          border: none;
          border-radius: 50px;
          padding: 12px 25px;
          font-weight: 600;
          font-size: 0.9rem;
          box-shadow: 0 4px 15px rgba(247, 37, 133, 0.3);
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          display: inline-flex;
          align-items: center;
          text-decoration: none;
        }

        .btn-add-course i {
          margin-right: 8px;
        }

        .btn-add-course:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 20px rgba(247, 37, 133, 0.4);
        }

        /* Boutons d'action (voir, modifier, supprimer) */
        .btn-view {
          background: linear-gradient(135deg, #4361ee, #3a0ca3);
          color: white !important;
          border: none;
          border-radius: 50px;
          padding: 8px 20px;
          font-weight: 600;
          transition: all 0.3s ease;
          box-shadow: 0 4px 10px rgba(67, 97, 238, 0.2);
          text-decoration: none;
          display: inline-block;
        }

        .btn-view:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 15px rgba(67, 97, 238, 0.3);
        }

        .btn-edit {
          background: linear-gradient(135deg, #f8961e, #f39c12);
          color: white !important;
          border: none;
          border-radius: 50px;
          padding: 8px 20px;
          font-weight: 600;
          transition: all 0.3s ease;
          box-shadow: 0 4px 10px rgba(248, 150, 30, 0.2);
          text-decoration: none;
          display: inline-block;
        }

        .btn-edit:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 15px rgba(248, 150, 30, 0.3);
        }

        .btn-delete {
          background: linear-gradient(135deg, #f94144, #e74c3c);
          color: white !important;
          border: none;
          border-radius: 50px;
          padding: 8px 20px;
          font-weight: 600;
          transition: all 0.3s ease;
          box-shadow: 0 4px 10px rgba(249, 65, 68, 0.2);
          text-decoration: none;
          display: inline-block;
        }

        .btn-delete:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 15px rgba(249, 65, 68, 0.3);
        }

        /* Liste des cours améliorée */
        .course-list {
          background: white;
          border-radius: 12px;
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          margin-top: 20px;
        }

        .course-list-item {
          padding: 20px;
          border-bottom: 1px solid #eee;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .course-list-item:hover {
          background-color: #f8f9fa;
          transform: translateX(5px);
        }

        .course-info {
          flex: 1;
        }

        .course-title {
          font-weight: 700;
          font-size: 1.2rem;
          color: #212529;
          margin-bottom: 5px;
        }

        .course-description {
          color: #666;
          margin-bottom: 10px;
        }

        .course-meta {
          display: flex;
          align-items: center;
          gap: 15px;
          font-size: 0.9rem;
          color: #777;
        }

        .course-meta i {
          color: #4361ee;
        }

        .course-actions {
          display: flex;
          gap: 10px;
        }

        /* En-tête de page avec dégradé */
        .page-header {
          margin: 30px 0;
          position: relative;
          padding-bottom: 15px;
          font-size: 2.5rem;
          font-weight: 800;
          color: #333;
          background: linear-gradient(135deg, #4361ee, #3a0ca3);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-shadow: 0 2px 10px rgba(67, 97, 238, 0.1);
        }

        .page-header::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 80px;
          height: 4px;
          background: linear-gradient(135deg, #4361ee, #3a0ca3);
          border-radius: 2px;
        }

        /* Bouton de déconnexion */
        .btn-logout {
          background-color: rgba(255, 255, 255, 0.15);
          color: white !important;
          border-radius: 50px;
          padding: 8px 20px;
          font-weight: 600;
          transition: all 0.3s ease;
          border: none;
          text-transform: uppercase;
          font-size: 0.85rem;
          letter-spacing: 0.5px;
          text-decoration: none;
        }

        .btn-logout:hover {
          background-color: rgba(255, 255, 255, 0.25);
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Badges de niveau */
        .level-badge {
          padding: 5px 10px;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 600;
        }

        .level-beginner {
          background-color: rgba(46, 204, 113, 0.2);
          color: #2ecc71;
        }

        .level-intermediate {
          background-color: rgba(248, 150, 30, 0.2);
          color: #f8961e;
        }

        .level-advanced {
          background-color: rgba(249, 65, 68, 0.2);
          color: #f94144;
        }

        /* Formulaire de recherche */
        .search-form {
          margin-bottom: 20px;
        }

        .search-input {
          border-radius: 50px 0 0 50px;
          padding: 12px 20px;
          border: 1px solid #e0e0e0;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .search-btn {
          border-radius: 0 50px 50px 0;
          background: linear-gradient(135deg, #4361ee, #3a0ca3);
          color: white;
          border: none;
          padding: 0 20px;
        }

        .filter-select {
          border-radius: 50px;
          padding: 10px 20px;
          border: 1px solid #e0e0e0;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-graduation-cap me-2"></i>E-Learn+
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">
                            <i class="fas fa-home me-1"></i> Accueil
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'course_list' %}">
                            <i class="fas fa-book me-1"></i> Cours
                        </a>
                    </li>
                    {% if user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:index' %}">
                            <i class="fas fa-cog me-1"></i> Administration
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <div class="navbar-nav">
                    {% if user.is_authenticated %}
                    <a href="{% url 'logout' %}" class="btn-logout">
                        <i class="fas fa-sign-out-alt me-1"></i> Déconnexion
                    </a>
                    {% else %}
                    <a href="{% url 'login' %}" class="nav-link">
                        <i class="fas fa-sign-in-alt me-1"></i> Connexion
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>

