{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
  .quiz-form-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: linear-gradient(135deg, #C8A8E9 0%, #A8D0F0 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  }

  .form-card {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
  }

  .form-title {
    color: #6B46C1;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
  }

  .form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #E5E7EB;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
  }

  .form-control:focus {
    outline: none;
    border-color: #C8A8E9;
    box-shadow: 0 0 0 3px rgba(200, 168, 233, 0.1);
  }

  .form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #6B46C1, #9333EA);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(107, 70, 193, 0.3);
  }

  .btn-secondary {
    background: #6B7280;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-secondary:hover {
    background: #4B5563;
    transform: translateY(-2px);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
  }

  .help-text {
    font-size: 0.875rem;
    color: #6B7280;
    margin-top: 0.25rem;
  }

  .correction-info {
    background: #F3F4F6;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 0.5rem;
  }

  .correction-info h4 {
    margin: 0 0 0.5rem 0;
    color: #374151;
    font-size: 1rem;
  }

  .correction-info p {
    margin: 0;
    font-size: 0.875rem;
    color: #6B7280;
  }
</style>
{% endblock %}

{% block content %}
<div class="quiz-form-container">
  <div class="form-card">
    <h1 class="form-title">
      <i class="fas fa-plus-circle"></i>
      Créer un nouveau quiz
    </h1>
    
    <p class="text-center text-gray-600 mb-4">
      Cours: <strong>{{ course.title }}</strong>
    </p>

    <form method="post">
      {% csrf_token %}
      
      <div class="form-group">
        <label for="title" class="form-label">
          <i class="fas fa-heading"></i> Titre du quiz *
        </label>
        <input type="text" id="title" name="title" class="form-control" required>
      </div>

      <div class="form-group">
        <label for="description" class="form-label">
          <i class="fas fa-align-left"></i> Description
        </label>
        <textarea id="description" name="description" class="form-control" rows="3" 
                  placeholder="Description optionnelle du quiz..."></textarea>
      </div>

      <div class="form-group">
        <label for="correction_type" class="form-label">
          <i class="fas fa-check-circle"></i> Type de correction *
        </label>
        <select id="correction_type" name="correction_type" class="form-control form-select" required>
          <option value="auto">Correction automatique</option>
          <option value="manual">Correction manuelle</option>
        </select>
        
        <div id="correction-info" class="correction-info">
          <div id="auto-info">
            <h4><i class="fas fa-robot"></i> Correction automatique</h4>
            <p>Les questions à choix multiples et vrai/faux seront corrigées automatiquement. Les réponses courtes peuvent aussi être corrigées automatiquement si une réponse exacte est fournie.</p>
          </div>
          <div id="manual-info" style="display: none;">
            <h4><i class="fas fa-user-edit"></i> Correction manuelle</h4>
            <p>Vous devrez corriger manuellement toutes les réponses des étudiants. Idéal pour les questions ouvertes et les dissertations.</p>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="time_limit" class="form-label">
              <i class="fas fa-clock"></i> Temps limite (minutes)
            </label>
            <input type="number" id="time_limit" name="time_limit" class="form-control" min="1" max="300">
            <div class="help-text">Laisser vide pour aucune limite de temps</div>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="form-group">
            <label for="max_attempts" class="form-label">
              <i class="fas fa-redo"></i> Tentatives maximum *
            </label>
            <input type="number" id="max_attempts" name="max_attempts" class="form-control" 
                   value="1" min="1" max="10" required>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="passing_score" class="form-label">
          <i class="fas fa-percentage"></i> Score minimum pour réussir (%) *
        </label>
        <input type="number" id="passing_score" name="passing_score" class="form-control" 
               value="60" min="0" max="100" required>
      </div>

      <div class="form-actions">
        <a href="{% url 'instructor_dashboard' %}" class="btn-secondary">
          <i class="fas fa-arrow-left"></i>
          Annuler
        </a>
        <button type="submit" class="btn-primary">
          <i class="fas fa-save"></i>
          Créer le quiz
        </button>
      </div>
    </form>
  </div>
</div>

<script>
document.getElementById('correction_type').addEventListener('change', function() {
  const autoInfo = document.getElementById('auto-info');
  const manualInfo = document.getElementById('manual-info');
  
  if (this.value === 'auto') {
    autoInfo.style.display = 'block';
    manualInfo.style.display = 'none';
  } else {
    autoInfo.style.display = 'none';
    manualInfo.style.display = 'block';
  }
});
</script>
{% endblock %}
