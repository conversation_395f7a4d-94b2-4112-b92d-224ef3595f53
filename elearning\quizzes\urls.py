from django.urls import path
from . import views

app_name = 'quizzes'

urlpatterns = [
    # URLs pour les étudiants
    path('<int:quiz_id>/take/', views.take_quiz, name='take_quiz'),
    path('results/<int:quiz_id>/', views.quiz_results, name='quiz_results'),

    # URLs pour les instructeurs
    path('instructor/', views.instructor_quiz_list, name='instructor_quiz_list'),
    path('instructor/select-course/', views.select_course_for_quiz, name='select_course_for_quiz'),
    path('create/<int:course_id>/', views.create_quiz, name='create_quiz'),
    path('edit/<int:quiz_id>/', views.edit_quiz, name='edit_quiz'),
    path('add-question/<int:quiz_id>/', views.add_question, name='add_question'),
]
