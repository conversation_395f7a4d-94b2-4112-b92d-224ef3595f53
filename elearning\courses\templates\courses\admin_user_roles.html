{% extends 'base.html' %}
{% load static %}

{% block title %}Gestion des Rôles{% endblock %}

{% block extra_css %}
<style>
  .role-management {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .header-section {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .users-table {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
  }

  .table th {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    font-weight: 600;
  }

  .role-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
  }

  .role-badge.admin {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
  }

  .role-badge.instructor {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
  }

  .role-badge.student {
    background: linear-gradient(135deg, #45b7d1, #96c93d);
    color: white;
  }

  .role-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.5rem;
    background: white;
    transition: all 0.3s ease;
  }

  .role-select:focus {
    border-color: #C8A8E9;
    box-shadow: 0 0 0 0.2rem rgba(200, 168, 233, 0.25);
  }

  .update-btn {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .update-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #333;
  }

  .status-active {
    color: #28a745;
    font-weight: bold;
  }

  .status-inactive {
    color: #dc3545;
    font-weight: bold;
  }

  .search-box {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  }

  .search-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    width: 100%;
    transition: all 0.3s ease;
  }

  .search-input:focus {
    border-color: #C8A8E9;
    box-shadow: 0 0 0 0.2rem rgba(200, 168, 233, 0.25);
  }
</style>
{% endblock %}

{% block content %}
<div class="role-management">
  <div class="header-section">
    <h1><i class="fas fa-users-cog"></i> Gestion des Rôles Utilisateurs</h1>
    <p>Gérez les rôles et permissions des utilisateurs de la plateforme</p>
  </div>

  <!-- Barre de recherche -->
  <div class="search-box">
    <div class="row align-items-center">
      <div class="col-md-8">
        <input type="text" id="searchInput" class="search-input" placeholder="🔍 Rechercher un utilisateur...">
      </div>
      <div class="col-md-4 text-end">
        <span class="text-muted">Total: {{ users.count }} utilisateurs</span>
      </div>
    </div>
  </div>

  <!-- Tableau des utilisateurs -->
  <div class="users-table">
    <table class="table table-hover mb-0" id="usersTable">
      <thead>
        <tr>
          <th><i class="fas fa-user"></i> Utilisateur</th>
          <th><i class="fas fa-envelope"></i> Email</th>
          <th><i class="fas fa-user-tag"></i> Rôle Actuel</th>
          <th><i class="fas fa-toggle-on"></i> Statut</th>
          <th><i class="fas fa-calendar"></i> Inscription</th>
          <th><i class="fas fa-cogs"></i> Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for user in users %}
        <tr>
          <td>
            <div class="d-flex align-items-center">
              <div class="avatar-circle me-3">
                {{ user.username|first|upper }}
              </div>
              <div>
                <strong>{{ user.username }}</strong>
                {% if user.first_name or user.last_name %}
                <br><small class="text-muted">{{ user.first_name }} {{ user.last_name }}</small>
                {% endif %}
              </div>
            </div>
          </td>
          <td>{{ user.email }}</td>
          <td>
            <span class="role-badge {{ user.role }}">
              {% if user.role == 'admin' %}
                <i class="fas fa-crown"></i> Administrateur
              {% elif user.role == 'instructor' %}
                <i class="fas fa-chalkboard-teacher"></i> Instructeur
              {% else %}
                <i class="fas fa-user-graduate"></i> Étudiant
              {% endif %}
            </span>
          </td>
          <td>
            {% if user.is_active %}
              <span class="status-active"><i class="fas fa-check-circle"></i> Actif</span>
            {% else %}
              <span class="status-inactive"><i class="fas fa-times-circle"></i> Inactif</span>
            {% endif %}
          </td>
          <td>
            <small>{{ user.date_joined|date:"d/m/Y" }}</small>
          </td>
          <td>
            <form method="post" class="d-flex align-items-center gap-2">
              {% csrf_token %}
              <input type="hidden" name="user_id" value="{{ user.id }}">
              <select name="role" class="role-select" onchange="this.form.submit()">
                <option value="student" {% if user.role == 'student' %}selected{% endif %}>Étudiant</option>
                <option value="instructor" {% if user.role == 'instructor' %}selected{% endif %}>Instructeur</option>
                <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Admin</option>
              </select>
            </form>
          </td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="6" class="text-center py-4">
            <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">Aucun utilisateur trouvé</h5>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<style>
.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-weight: bold;
  font-size: 1.2rem;
}
</style>

<script>
// Fonction de recherche
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#usersTable tbody tr');
    
    tableRows.forEach(row => {
        const username = row.cells[0].textContent.toLowerCase();
        const email = row.cells[1].textContent.toLowerCase();
        
        if (username.includes(searchTerm) || email.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
