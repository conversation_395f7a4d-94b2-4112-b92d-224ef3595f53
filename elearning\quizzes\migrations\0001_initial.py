# Generated by Django 5.2.1 on 2025-06-25 23:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('courses', '0008_course_is_free_course_price'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('correction_type', models.CharField(choices=[('auto', 'Correction automatique'), ('manual', 'Correction manuelle')], default='auto', max_length=10)),
                ('time_limit', models.PositiveIntegerField(blank=True, help_text='Temps limite en minutes', null=True)),
                ('max_attempts', models.PositiveIntegerField(default=1, help_text='Nombre maximum de tentatives')),
                ('passing_score', models.PositiveIntegerField(default=60, help_text='Score minimum pour réussir (%)')),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to='courses.course')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_quizzes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Quizzes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Choix multiple'), ('true_false', 'Vrai/Faux'), ('short_answer', 'Réponse courte'), ('essay', 'Dissertation')], default='multiple_choice', max_length=20)),
                ('text', models.TextField()),
                ('points', models.PositiveIntegerField(default=1)),
                ('order', models.PositiveIntegerField(default=0)),
                ('option_a', models.CharField(blank=True, max_length=255)),
                ('option_b', models.CharField(blank=True, max_length=255)),
                ('option_c', models.CharField(blank=True, max_length=255)),
                ('option_d', models.CharField(blank=True, max_length=255)),
                ('correct_option', models.CharField(blank=True, choices=[('A', 'A'), ('B', 'B'), ('C', 'C'), ('D', 'D')], max_length=1)),
                ('correct_answer_boolean', models.BooleanField(blank=True, null=True)),
                ('correct_answer_text', models.TextField(blank=True)),
                ('explanation', models.TextField(blank=True, help_text='Explication de la bonne réponse')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='quizzes.quiz')),
            ],
            options={
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='QuizSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('in_progress', 'En cours'), ('submitted', 'Soumis'), ('graded', 'Corrigé')], default='in_progress', max_length=20)),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('max_score', models.PositiveIntegerField(default=0)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('attempt_number', models.PositiveIntegerField(default=1)),
                ('time_taken', models.DurationField(blank=True, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('graded_at', models.DateTimeField(blank=True, null=True)),
                ('feedback', models.TextField(blank=True)),
                ('graded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_submissions', to=settings.AUTH_USER_MODEL)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='quizzes.quiz')),
                ('student', models.ForeignKey(limit_choices_to={'role': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='quiz_submissions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
                'unique_together': {('quiz', 'student', 'attempt_number')},
            },
        ),
        migrations.CreateModel(
            name='QuizAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('selected_option', models.CharField(blank=True, max_length=1)),
                ('boolean_answer', models.BooleanField(blank=True, null=True)),
                ('text_answer', models.TextField(blank=True)),
                ('is_correct', models.BooleanField(blank=True, null=True)),
                ('points_awarded', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('instructor_feedback', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quizzes.question')),
                ('submission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='quizzes.quizsubmission')),
            ],
            options={
                'unique_together': {('submission', 'question')},
            },
        ),
    ]
