from django.shortcuts import render
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse, HttpResponse
from django.db.models import Count, Avg, Sum
from django.utils import timezone
from datetime import datetime, timedelta
import csv
import json

from users.models import User
from courses.models import Course, Lesson, Progress
from messaging.models import Message
from .models import UserStatistics, CourseStatistics, SystemStatistics

def is_admin(user):
    return user.is_authenticated and user.role == 'admin'

@login_required
@user_passes_test(is_admin)
def admin_statistics(request):
    """Vue principale des statistiques pour l'administrateur"""
    
    # Statistiques générales
    total_users = User.objects.count()
    total_students = User.objects.filter(role='student').count()
    total_instructors = User.objects.filter(role='instructor').count()
    total_courses = Course.objects.count()
    total_lessons = Lesson.objects.count()
    
    # Statistiques des cours
    published_courses = Course.objects.filter(is_published=True).count()
    total_enrollments = sum(course.students.count() for course in Course.objects.all())
    
    # Activité récente (7 derniers jours)
    week_ago = timezone.now() - timedelta(days=7)
    new_users_week = User.objects.filter(date_joined__gte=week_ago).count()
    new_courses_week = Course.objects.filter(created_at__gte=week_ago).count()
    
    # Cours les plus populaires
    popular_courses = Course.objects.annotate(
        enrollment_count=Count('students')
    ).order_by('-enrollment_count')[:5]
    
    # Instructeurs les plus actifs
    active_instructors = User.objects.filter(role='instructor').annotate(
        course_count=Count('courses')
    ).order_by('-course_count')[:5]
    
    # Données pour les graphiques
    # Évolution des inscriptions par mois (6 derniers mois)
    months_data = []
    for i in range(6):
        month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start + timedelta(days=30)
        month_users = User.objects.filter(
            date_joined__gte=month_start,
            date_joined__lt=month_end
        ).count()
        months_data.append({
            'month': month_start.strftime('%B %Y'),
            'users': month_users
        })
    
    months_data.reverse()
    
    context = {
        'total_users': total_users,
        'total_students': total_students,
        'total_instructors': total_instructors,
        'total_courses': total_courses,
        'total_lessons': total_lessons,
        'published_courses': published_courses,
        'total_enrollments': total_enrollments,
        'new_users_week': new_users_week,
        'new_courses_week': new_courses_week,
        'popular_courses': popular_courses,
        'active_instructors': active_instructors,
        'months_data': json.dumps(months_data),
    }
    
    return render(request, 'analytics/admin_statistics.html', context)

@login_required
@user_passes_test(is_admin)
def export_users_csv(request):
    """Exporter la liste des utilisateurs en CSV"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="utilisateurs.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['ID', 'Nom d\'utilisateur', 'Email', 'Rôle', 'Date d\'inscription', 'Dernière connexion'])
    
    users = User.objects.all().order_by('-date_joined')
    for user in users:
        writer.writerow([
            user.id,
            user.username,
            user.email,
            user.get_role_display(),
            user.date_joined.strftime('%Y-%m-%d %H:%M'),
            user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Jamais'
        ])
    
    return response

@login_required
@user_passes_test(is_admin)
def export_courses_csv(request):
    """Exporter la liste des cours en CSV"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="cours.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['ID', 'Titre', 'Instructeur', 'Catégorie', 'Niveau', 'Publié', 'Étudiants inscrits', 'Date de création'])
    
    courses = Course.objects.all().order_by('-created_at')
    for course in courses:
        writer.writerow([
            course.id,
            course.title,
            course.instructor.username,
            course.get_category_display(),
            course.get_level_display(),
            'Oui' if course.is_published else 'Non',
            course.students.count(),
            course.created_at.strftime('%Y-%m-%d %H:%M')
        ])
    
    return response

@login_required
@user_passes_test(is_admin)
def statistics_api(request):
    """API pour récupérer les statistiques en JSON"""
    data = {
        'users': {
            'total': User.objects.count(),
            'students': User.objects.filter(role='student').count(),
            'instructors': User.objects.filter(role='instructor').count(),
        },
        'courses': {
            'total': Course.objects.count(),
            'published': Course.objects.filter(is_published=True).count(),
            'draft': Course.objects.filter(is_published=False).count(),
        },
        'activity': {
            'total_enrollments': sum(course.students.count() for course in Course.objects.all()),
            'total_lessons': Lesson.objects.count(),
        }
    }
    
    return JsonResponse(data)
