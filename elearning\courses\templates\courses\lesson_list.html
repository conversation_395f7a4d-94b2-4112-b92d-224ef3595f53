{% extends 'base.html' %}
{% block title %}Lessons in {{ module.title }}{% endblock %}
{% block content %}

<div class="container py-4">
  <h3>📚 Lessons in Module: <strong>{{ module.title }}</strong></h3>

  <a href="{% url 'add_lesson' module.id %}" class="btn btn-primary my-3">➕ Add New Lesson</a>

  <ul class="list-group">
    {% for lesson in lessons %}
      <li class="list-group-item d-flex justify-content-between align-items-center">
        {{ lesson.title }}
        <div>
          <a href="{% url 'edit_lesson' lesson.id %}" class="btn btn-sm btn-warning me-2">Edit</a>
          <a href="{% url 'delete_lesson' lesson.id %}" class="btn btn-sm btn-danger">Delete</a>
        </div>
      </li>
    {% empty %}
      <li class="list-group-item">No lessons found.</li>
    {% endfor %}
  </ul>
</div>

{% endblock %}
