// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', function() {
  // Activer les tooltips Bootstrap
  var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
  
  // Fermer automatiquement les alertes après 5 secondes
  setTimeout(function() {
    var alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
      var bsAlert = new bootstrap.Alert(alert);
      bsAlert.close();
    });
  }, 5000);
  
  // Animations pour les cartes de cours
  const courseCards = document.querySelectorAll('.course-card');
  courseCards.forEach((card, index) => {
    // Animation d'entrée avec délai
    setTimeout(() => {
      card.classList.add('animate-fade-in');
    }, 100 * index);
    
    // Animation au survol
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-10px)';
      this.style.boxShadow = '0 20px 30px rgba(0, 0, 0, 0.15)';
      
      // Animer l'image
      const img = this.querySelector('.card-img-top');
      if (img) {
        img.style.transform = 'scale(1.1)';
      }
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
      
      // Réinitialiser l'animation de l'image
      const img = this.querySelector('.card-img-top');
      if (img) {
        img.style.transform = 'scale(1)';
      }
    });
  });
  
  // Animation pour les boutons
  const buttons = document.querySelectorAll('.btn-view, .btn-add-course');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-3px)';
    });
    
    button.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });
  });
  
  // Animation pour la barre de recherche
  const searchInput = document.querySelector('.search-input');
  if (searchInput) {
    searchInput.addEventListener('focus', function() {
      this.parentElement.style.transform = 'scale(1.02)';
      this.parentElement.style.boxShadow = '0 0 0 4px rgba(67, 97, 238, 0.15)';
    });
    
    searchInput.addEventListener('blur', function() {
      this.parentElement.style.transform = 'scale(1)';
      this.parentElement.style.boxShadow = 'none';
    });
  }
});
