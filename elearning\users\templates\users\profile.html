{% extends 'base.html' %}

{% block title %}Mon Profil - E-Learn+{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row">
    <div class="col-lg-4">
      <div class="card shadow-sm border-0 mb-4">
        <div class="card-body text-center p-4">
          <img src="{{ user.profile.avatar.url|default:'https://via.placeholder.com/150' }}" alt="{{ user.username }}" class="rounded-circle mb-3" width="150" height="150">
          <h3 class="mb-1">{{ user.get_full_name|default:user.username }}</h3>
          <p class="text-muted">{{ user.email }}</p>
          
          <div class="d-grid gap-2 mt-4">
            <button class="btn btn-outline-primary" type="button">Modifier la photo</button>
            <button class="btn btn-outline-secondary" type="button">Changer le mot de passe</button>
          </div>
        </div>
      </div>
      
      <div class="card shadow-sm border-0">
        <div class="card-body p-4">
          <h5 class="card-title mb-3">Statistiques</h5>
          <div class="d-flex justify-content-between mb-2">
            <span>Cours suivis</span>
            <span class="badge bg-primary">12</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Cours terminés</span>
            <span class="badge bg-success">8</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Certificats obtenus</span>
            <span class="badge bg-info">5</span>
          </div>
          <div class="d-flex justify-content-between">
            <span>Points de fidélité</span>
            <span class="badge bg-warning">350</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-8">
      <div class="card shadow-sm border-0 mb-4">
        <div class="card-body p-4">
          <h4 class="card-title mb-4">Informations personnelles</h4>
          
          <form>
            <div class="row g-3">
              <div class="col-md-6">
                <label for="firstName" class="form-label">Prénom</label>
                <input type="text" class="form-control" id="firstName" value="{{ user.first_name }}">
              </div>
              <div class="col-md-6">
                <label for="lastName" class="form-label">Nom</label>
                <input type="text" class="form-control" id="lastName" value="{{ user.last_name }}">
              </div>
              <div class="col-md-6">
                <label for="username" class="form-label">Nom d'utilisateur</label>
                <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
              </div>
              <div class="col-md-6">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" value="{{ user.email }}">
              </div>
              <div class="col-md-6">
                <label for="phone" class="form-label">Téléphone</label>
                <input type="tel" class="form-control" id="phone" value="{{ user.profile.phone|default:'' }}">
              </div>
              <div class="col-md-6">
                <label for="birthdate" class="form-label">Date de naissance</label>
                <input type="date" class="form-control" id="birthdate" value="{{ user.profile.birthdate|date:'Y-m-d'|default:'' }}">
              </div>
              <div class="col-12">
                <label for="bio" class="form-label">Biographie</label>
                <textarea class="form-control" id="bio" rows="4">{{ user.profile.bio|default:'' }}</textarea>
              </div>
              <div class="col-12 mt-4">
                <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
              </div>
            </div>
          </form>
        </div>
      </div>
      
      <div class="card shadow-sm border-0">
        <div class="card-body p-4">
          <h4 class="card-title mb-4">Préférences</h4>
          
          <div class="mb-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
              <label class="form-check-label" for="emailNotifications">Recevoir des notifications par email</label>
            </div>
            <div class="form-text text-muted">Recevez des mises à jour sur vos cours, les nouveaux contenus et les promotions.</div>
          </div>
          
          <div class="mb-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="publicProfile" checked>
              <label class="form-check-label" for="publicProfile">Profil public</label>
            </div>
            <div class="form-text text-muted">Permettre aux autres utilisateurs de voir votre profil et vos activités.</div>
          </div>
          
          <div class="mb-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="darkMode">
              <label class="form-check-label" for="darkMode">Mode sombre par défaut</label>
            </div>
            <div class="form-text text-muted">Utiliser le mode sombre comme thème par défaut.</div>
          </div>
          
          <button type="button" class="btn btn-primary mt-3">Enregistrer les préférences</button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}