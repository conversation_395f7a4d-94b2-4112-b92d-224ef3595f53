<nav class="navbar navbar-expand-lg navbar-light sticky-top">
  <div class="container">
    <a class="navbar-brand" href="{% url 'home' %}">
      <span style="background: var(--gradient-primary); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700; font-size: 1.5rem;">E-Learn+</span>
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav me-auto">
        <li class="nav-item">
          <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="{% url 'home' %}">Accueil</a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'courses' in request.path %}active{% endif %}" href="{% url 'course_list' %}">Cours</a>
        </li>
        <!-- Retiré le lien vers instructor_list qui n'existe pas -->
        <li class="nav-item">
          <a class="nav-link {% if 'about' in request.path %}active{% endif %}" href="{% url 'about' %}">À propos</a>
        </li>
        <li class="nav-item">
          <a class="nav-link {% if 'contact' in request.path %}active{% endif %}" href="{% url 'contact' %}">Contact</a>
        </li>
      </ul>
      
      <ul class="navbar-nav">
        <!-- Bouton de changement de thème -->
        <li class="nav-item me-2">
          <button id="theme-toggle" class="btn btn-sm" style="background: var(--light); border-radius: var(--radius-pill); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
            <i id="theme-icon" class="fas fa-sun"></i>
          </button>
        </li>
        
        {% if user.is_authenticated %}
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <img src="{{ user.profile.avatar.url|default:'https://via.placeholder.com/30' }}" alt="{{ user.username }}" class="rounded-circle me-1" width="30" height="30">
              {{ user.username }}
            </a>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
              <li><a class="dropdown-item" href="{% url 'profile' %}"><i class="fas fa-user me-2"></i>Mon profil</a></li>
              <li><a class="dropdown-item" href="{% url 'my_courses' %}"><i class="fas fa-book me-2"></i>Mes cours</a></li>
              {% if user.is_instructor %}
              <li><a class="dropdown-item" href="{% url 'instructor_dashboard' %}"><i class="fas fa-chalkboard-teacher me-2"></i>Espace instructeur</a></li>
              {% endif %}
              {% if user.is_staff %}
              <li><a class="dropdown-item" href="{% url 'admin_dashboard' %}"><i class="fas fa-cog me-2"></i>Administration</a></li>
              {% endif %}
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
            </ul>
          </li>
        {% else %}
          <li class="nav-item">
            <a href="{% url 'login' %}" class="btn btn-outline-primary me-2" style="border-radius: var(--radius-pill); border: 1px solid var(--primary);">Connexion</a>
          </li>
          <li class="nav-item">
            <a href="{% url 'register' %}" class="btn btn-primary" style="border-radius: var(--radius-pill);">Inscription</a>
          </li>
        {% endif %}
      </ul>
    </div>
  </div>
</nav>
