{% extends 'base.html' %}
{% load static %}

{% block title %}Supprimer le module {{ module.title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .delete-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .delete-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .warning-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #fff3cd;
  }

  .delete-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
  }

  .module-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 15px;
    margin: 2rem 0;
    text-align: left;
  }

  .module-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .course-title {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .warning-list {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 2rem 0;
    text-align: left;
  }

  .warning-list h5 {
    color: #856404;
    margin-bottom: 1rem;
  }

  .warning-list ul {
    color: #856404;
    margin: 0;
    padding-left: 1.5rem;
  }

  .warning-list li {
    margin-bottom: 0.5rem;
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
  }

  .btn-cancel {
    background: #6c757d;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
  }

  .btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
  }

  .btn-delete {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
  }

  .btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
  }

  .confirmation-text {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
  <div class="delete-header">
    <div class="warning-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    <h1>Supprimer le Module</h1>
    <p>Cette action est irréversible</p>
  </div>

  <div class="delete-card">
    <p class="confirmation-text">
      Êtes-vous sûr de vouloir supprimer ce module ? 
      Cette action ne peut pas être annulée.
    </p>

    <!-- Informations du module -->
    <div class="module-info">
      <div class="module-title">{{ module.title }}</div>
      <div class="course-title">Cours : {{ course.title }}</div>
      <p class="text-muted">{{ module.description|truncatewords:20 }}</p>
    </div>

    <!-- Avertissements -->
    <div class="warning-list">
      <h5><i class="fas fa-exclamation-triangle"></i> Conséquences de la suppression :</h5>
      <ul>
        <li>Le module sera définitivement supprimé</li>
        <li>Toutes les leçons associées seront supprimées</li>
        <li>Les étudiants perdront l'accès au contenu</li>
        <li>L'historique de progression sera perdu</li>
        <li>Les fichiers et ressources seront supprimés</li>
        <li>Cette action ne peut pas être annulée</li>
      </ul>
    </div>

    <!-- Boutons d'action -->
    <div class="action-buttons">
      <a href="{% url 'instructor_course_detail' course.id %}" class="btn-cancel">
        <i class="fas fa-times"></i>
        Annuler
      </a>
      
      <form method="post" style="display: inline;">
        {% csrf_token %}
        <button type="submit" class="btn-delete">
          <i class="fas fa-trash"></i>
          Supprimer Définitivement
        </button>
      </form>
    </div>
  </div>
</div>
{% endblock %}
