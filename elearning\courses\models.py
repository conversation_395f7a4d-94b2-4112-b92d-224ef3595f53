from django.db import models
from django.conf import settings
from django.utils.text import slugify

class Course(models.Model):
    CATEGORY_CHOICES = [
        ('programming', 'Programmation'),
        ('design', 'Design'),
        ('business', 'Business'),
        ('marketing', 'Marketing'),
        ('personal', 'Développement personnel'),
        ('other', 'Autre'),
    ]
    
    LEVEL_CHOICES = [
        ('beginner', 'Débutant'),
        ('intermediate', 'Intermédiaire'),
        ('advanced', 'Avancé'),
    ]
    
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True, blank=True)
    description = models.TextField()
    instructor = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='courses')
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES, default='other')
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, default='beginner')
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    image = models.ImageField(upload_to='courses/', blank=True, null=True)
    duration = models.CharField(max_length=50, default='8 heures')
    rating = models.DecimalField(max_digits=3, decimal_places=1, default=4.5)
    students = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='enrolled_courses', blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, help_text="Prix du cours en euros")
    is_free = models.BooleanField(default=False, help_text="Cours gratuit")
    
    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def get_total_lessons(self):
        """Retourne le nombre total de leçons dans le cours"""
        return sum(module.lessons.count() for module in self.modules.all())

    def get_completion_rate(self, student):
        """Calcule le taux de completion pour un étudiant donné"""
        total_lessons = self.get_total_lessons()
        if total_lessons == 0:
            return 0
        completed_lessons = self.student_progress.filter(
            student=student,
            completed=True
        ).count()
        return (completed_lessons / total_lessons) * 100

    def is_completed_by(self, student):
        """Vérifie si le cours est terminé par un étudiant"""
        return self.get_completion_rate(student) == 100

class Module(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='modules')
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.course.title} - {self.title}"

class Lesson(models.Model):
    CONTENT_TYPES = (
        ('text', 'Texte'),
        ('video', 'Vidéo'),
        ('quiz', 'Quiz'),
        ('assignment', 'Devoir'),
    )

    title = models.CharField(max_length=200)
    content = models.TextField()
    module = models.ForeignKey(Module, on_delete=models.CASCADE, related_name='lessons')
    order = models.PositiveIntegerField(default=0)
    video_url = models.URLField(blank=True, null=True)
    duration = models.DurationField(blank=True, null=True)
    is_free = models.BooleanField(default=False)
    content_type = models.CharField(max_length=20, choices=CONTENT_TYPES, default='text')
    file = models.FileField(upload_to='lessons/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.module.title} - {self.title}"

class LessonFile(models.Model):
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name='files')
    title = models.CharField(max_length=200)
    file = models.FileField(upload_to='lesson_files/')
    file_type = models.CharField(max_length=50, choices=[
        ('pdf', 'PDF'),
        ('doc', 'Document'),
        ('video', 'Vidéo'),
        ('audio', 'Audio'),
        ('image', 'Image'),
        ('other', 'Autre')
    ], default='other')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.lesson.title} - {self.title}"

class Progress(models.Model):
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='progress')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='student_progress')
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name='student_progress')
    completed = models.BooleanField(default=False)
    completion_date = models.DateTimeField(blank=True, null=True)
    time_spent = models.DurationField(null=True, blank=True)

    class Meta:
        unique_together = ['student', 'lesson']

    def __str__(self):
        return f"{self.student.username} - {self.lesson.title} - {'Terminé' if self.completed else 'En cours'}"



