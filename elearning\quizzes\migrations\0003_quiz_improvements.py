# Generated manually for quiz improvements

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('quizzes', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0008_course_is_free_course_price'),
    ]

    operations = [
        # Modifier le modèle Quiz
        migrations.AddField(
            model_name='quiz',
            name='correction_type',
            field=models.CharField(choices=[('auto', 'Correction automatique'), ('manual', 'Correction manuelle')], default='auto', max_length=10),
        ),
        migrations.AddField(
            model_name='quiz',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='quiz',
            name='time_limit',
            field=models.PositiveIntegerField(blank=True, help_text='Temps limite en minutes', null=True),
        ),
        migrations.AddField(
            model_name='quiz',
            name='max_attempts',
            field=models.PositiveIntegerField(default=1, help_text='Nombre maximum de tentatives'),
        ),
        migrations.AddField(
            model_name='quiz',
            name='passing_score',
            field=models.PositiveIntegerField(default=60, help_text='Score minimum pour réussir (%)'),
        ),
        migrations.AddField(
            model_name='quiz',
            name='is_published',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='quiz',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='quiz',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='quiz',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='created_quizzes', to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        
        # Modifier le modèle Question
        migrations.AddField(
            model_name='question',
            name='question_type',
            field=models.CharField(choices=[('multiple_choice', 'Choix multiple'), ('true_false', 'Vrai/Faux'), ('short_answer', 'Réponse courte'), ('essay', 'Dissertation')], default='multiple_choice', max_length=20),
        ),
        migrations.AddField(
            model_name='question',
            name='points',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name='question',
            name='order',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='question',
            name='correct_answer_boolean',
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='question',
            name='correct_answer_text',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='question',
            name='explanation',
            field=models.TextField(blank=True, help_text='Explication de la bonne réponse'),
        ),
        migrations.AddField(
            model_name='question',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='question',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        
        # Modifier les champs existants
        migrations.AlterField(
            model_name='quiz',
            name='course',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to='courses.course'),
        ),
        migrations.AlterField(
            model_name='question',
            name='quiz',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='quizzes.quiz'),
        ),
        migrations.AlterField(
            model_name='question',
            name='option_a',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='question',
            name='option_b',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='question',
            name='option_c',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='question',
            name='option_d',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='question',
            name='correct_option',
            field=models.CharField(blank=True, choices=[('A', 'A'), ('B', 'B'), ('C', 'C'), ('D', 'D')], max_length=1),
        ),
    ]
