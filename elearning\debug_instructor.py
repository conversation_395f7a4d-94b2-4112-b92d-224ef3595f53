#!/usr/bin/env python
"""
Script de débogage pour l'espace instructeur
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserProfile
from courses.models import Course, Module, Lesson

def debug_instructor():
    print("=== DEBUG ESPACE INSTRUCTEUR ===\n")
    
    # 1. Vérifier les utilisateurs
    print("1. UTILISATEURS:")
    users = User.objects.all()
    for user in users:
        try:
            profile = user.userprofile
            print(f"   - {user.username} ({user.email}) - Rôle: {profile.role}")
        except UserProfile.DoesNotExist:
            print(f"   - {user.username} ({user.email}) - Pas de profil")
    
    # 2. Vérifier les instructeurs
    print("\n2. INSTRUCTEURS:")
    instructors = UserProfile.objects.filter(role='instructor')
    if instructors:
        for instructor in instructors:
            print(f"   - {instructor.user.username}")
            courses = Course.objects.filter(instructor=instructor.user)
            print(f"     Cours: {courses.count()}")
            for course in courses:
                modules = Module.objects.filter(course=course)
                print(f"       - {course.title} ({modules.count()} modules)")
    else:
        print("   Aucun instructeur trouvé")
    
    # 3. Créer un instructeur de test si nécessaire
    print("\n3. CRÉATION D'UN INSTRUCTEUR DE TEST:")
    username = 'instructor_test'
    if not User.objects.filter(username=username).exists():
        user = User.objects.create_user(
            username=username,
            email='<EMAIL>',
            password='test123',
            first_name='Test',
            last_name='Instructor'
        )
        profile = UserProfile.objects.create(user=user, role='instructor')
        print(f"   ✓ Instructeur créé: {username} / test123")
        
        # Créer un cours de test
        course = Course.objects.create(
            title='Cours de Test',
            description='Cours pour tester les fonctionnalités',
            instructor=user,
            category='programming',
            level='beginner',
            duration='1 semaine'
        )
        print(f"   ✓ Cours de test créé: {course.title}")
    else:
        print(f"   L'instructeur {username} existe déjà")
    
    print("\n=== INSTRUCTIONS POUR TESTER ===")
    print("1. Connectez-vous avec: instructor_test / test123")
    print("2. Allez sur: http://127.0.0.1:8000/users/login/")
    print("3. Puis sur: http://127.0.0.1:8000/instructor-dashboard/")
    print("4. Testez la création de modules et leçons")

if __name__ == '__main__':
    debug_instructor()
