{% extends 'base.html' %}
{% load static %}

{% block title %}Gestion des Instructeurs | E-Learn+{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <!-- Sidebar de navigation -->
    <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
      <div class="position-sticky pt-3">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'dashboard' %}active{% endif %}" href="{% url 'admin_dashboard' %}">
              <i class="fas fa-tachometer-alt me-2"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'students' %}active{% endif %}" href="{% url 'admin_users' %}">
              <i class="fas fa-user-graduate me-2"></i>
              Étudiants
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'instructors' %}active{% endif %}" href="{% url 'admin_instructors' %}">
              <i class="fas fa-chalkboard-teacher me-2"></i>
              Instructeurs
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'courses' %}active{% endif %}" href="{% url 'admin_courses' %}">
              <i class="fas fa-book me-2"></i>
              Cours
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {% if active_tab == 'settings' %}active{% endif %}" href="{% url 'admin_settings' %}">
              <i class="fas fa-cog me-2"></i>
              Paramètres
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Contenu principal -->
    <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
      <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Gestion des Instructeurs</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInstructorModal">
          <i class="fas fa-plus me-1"></i> Ajouter un instructeur
        </button>
      </div>

      <!-- Liste des instructeurs -->
      <div class="card shadow-sm mb-4">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Nom d'utilisateur</th>
                  <th>Email</th>
                  <th>Date d'inscription</th>
                  <th>Cours enseignés</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for instructor in instructors %}
                <tr>
                  <td>{{ instructor.username }}</td>
                  <td>{{ instructor.email }}</td>
                  <td>{{ instructor.date_joined|date:"d/m/Y" }}</td>
                  <td>{{ instructor.course_set.count }}</td>
                  <td>
                    <div class="btn-group">
                      <a href="#" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i>
                      </a>
                      <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteInstructorModal{{ instructor.id }}">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="5" class="text-center py-4">
                    <div class="py-3">
                      <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                      <p class="mb-1">Aucun instructeur trouvé</p>
                      <p class="text-muted">Commencez par ajouter un nouvel instructeur</p>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
          <li class="page-item disabled">
            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Précédent</a>
          </li>
          <li class="page-item active"><a class="page-link" href="#">1</a></li>
          <li class="page-item"><a class="page-link" href="#">2</a></li>
          <li class="page-item"><a class="page-link" href="#">3</a></li>
          <li class="page-item">
            <a class="page-link" href="#">Suivant</a>
          </li>
        </ul>
      </nav>
    </main>
  </div>
</div>

<!-- Modal pour ajouter un instructeur -->
<div class="modal fade" id="addInstructorModal" tabindex="-1" aria-labelledby="addInstructorModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addInstructorModalLabel">Ajouter un instructeur</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="post" action="{% url 'admin_instructors' %}">
        <div class="modal-body">
          {% csrf_token %}
          <div class="mb-3">
            <label for="username" class="form-label">Nom d'utilisateur</label>
            <input type="text" class="form-control" id="username" name="username" required>
          </div>
          <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <input type="email" class="form-control" id="email" name="email" required>
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">Mot de passe</label>
            <input type="password" class="form-control" id="password" name="password" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
          <button type="submit" class="btn btn-primary">Ajouter</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modals de suppression pour chaque instructeur -->
{% for instructor in instructors %}
<div class="modal fade" id="deleteInstructorModal{{ instructor.id }}" tabindex="-1" aria-labelledby="deleteInstructorModalLabel{{ instructor.id }}" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteInstructorModalLabel{{ instructor.id }}">Confirmer la suppression</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Êtes-vous sûr de vouloir supprimer l'instructeur <strong>{{ instructor.username }}</strong> ?</p>
        <p class="text-danger"><strong>Attention :</strong> Cette action supprimera également tous les cours associés à cet instructeur et ne peut pas être annulée.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Annuler</button>
        <form method="POST" action="{% url 'delete_instructor' instructor.id %}">
          {% csrf_token %}
          <button type="submit" class="btn btn-danger">
            <i class="fas fa-trash me-1"></i> Supprimer définitivement
          </button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endfor %}
{% endblock %}


