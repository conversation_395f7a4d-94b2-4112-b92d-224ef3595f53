from django.db import models
from django.conf import settings
from courses.models import Course

class UserStatistics(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='statistics')
    total_courses_enrolled = models.PositiveIntegerField(default=0)
    total_courses_completed = models.PositiveIntegerField(default=0)
    total_lessons_completed = models.PositiveIntegerField(default=0)
    total_time_spent = models.DurationField(null=True, blank=True)
    last_activity = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Statistiques de {self.user.username}"

class CourseStatistics(models.Model):
    course = models.OneToOneField(Course, on_delete=models.CASCADE, related_name='statistics')
    total_enrollments = models.PositiveIntegerField(default=0)
    total_completions = models.PositiveIntegerField(default=0)
    average_completion_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    total_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    def __str__(self):
        return f"Statistiques du cours {self.course.title}"

class SystemStatistics(models.Model):
    date = models.DateField(unique=True)
    total_users = models.PositiveIntegerField(default=0)
    total_students = models.PositiveIntegerField(default=0)
    total_instructors = models.PositiveIntegerField(default=0)
    total_courses = models.PositiveIntegerField(default=0)
    total_lessons = models.PositiveIntegerField(default=0)
    daily_active_users = models.PositiveIntegerField(default=0)
    new_registrations = models.PositiveIntegerField(default=0)
    new_enrollments = models.PositiveIntegerField(default=0)
    
    class Meta:
        ordering = ['-date']
    
    def __str__(self):
        return f"Statistiques système du {self.date}"
