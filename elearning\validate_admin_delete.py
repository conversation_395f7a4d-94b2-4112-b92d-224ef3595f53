#!/usr/bin/env python
"""
Script de validation pour la suppression de cours admin
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

from courses.models import Course

def validate_delete():
    print("=== VALIDATION SUPPRESSION COURS ===\n")
    
    # Vérifier si le cours de test existe encore
    test_course_id = 8
    try:
        course = Course.objects.get(id=test_course_id)
        print(f"📋 Cours de test trouvé: {course.title}")
        print(f"   - ID: {course.id}")
        print(f"   - Instructeur: {course.instructor.username}")
        print(f"   - Étudiants inscrits: {course.students.count()}")
        print(f"   - C<PERSON>é le: {course.created_at}")
        
        print(f"\n🔗 URL de test: http://127.0.0.1:8000/admin-dashboard/courses/{course.id}/delete/")
        print(f"✅ Template disponible: courses/admin_confirm_delete.html")
        
    except Course.DoesNotExist:
        print(f"✅ Le cours ID {test_course_id} a été supprimé avec succès!")
        print("La fonctionnalité de suppression fonctionne correctement.")
    
    # Lister tous les cours restants
    print(f"\n📚 COURS RESTANTS:")
    courses = Course.objects.all()
    for course in courses:
        print(f"   - {course.title} (ID: {course.id})")
    
    print(f"\n✅ VALIDATION TERMINÉE!")

if __name__ == "__main__":
    validate_delete()
