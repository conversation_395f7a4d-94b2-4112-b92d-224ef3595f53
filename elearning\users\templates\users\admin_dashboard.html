{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/admin_style.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  /* Dashboard moderne avec thème baby violet/baby blue */
  .dashboard-container {
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    min-height: 100vh;
    padding: 40px 20px;
  }

  .dashboard-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 50px 40px;
    border-radius: 24px;
    margin-bottom: 40px;
    box-shadow: 0 15px 35px rgba(200, 168, 233, 0.3);
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .dashboard-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }

  .dashboard-title {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    position: relative;
    z-index: 2;
  }

  .dashboard-title i {
    margin-right: 20px;
    opacity: 0.9;
  }

  .dashboard-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    margin: 0;
    position: relative;
    z-index: 2;
  }

  /* Cartes de statistiques modernes */
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
  }

  .stat-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: none;
  }

  .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  }

  .stat-card.students::before {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-card.courses::before {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stat-card.instructors::before {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .stat-card.modules::before {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
    color: white;
  }

  .stat-card.students .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-card.courses .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stat-card.instructors .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .stat-card.modules .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
  }

  .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
  }

  /* Actions d'administration */
  .admin-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
  }

  .action-card {
    background: white;
    border-radius: 20px;
    padding: 35px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: flex-start;
    gap: 25px;
    position: relative;
    overflow: hidden;
  }

  .action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .action-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  }

  .action-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    flex-shrink: 0;
  }

  .action-icon.users {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .action-icon.courses {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .action-icon.instructors {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .action-content {
    flex: 1;
  }

  .action-content h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 15px 0;
  }

  .action-content p {
    color: #718096;
    margin: 0 0 25px 0;
    line-height: 1.6;
  }

  .btn-action {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 15px;
    font-weight: 600;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  .btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .dashboard-container {
      padding: 20px 15px;
    }

    .dashboard-header {
      padding: 30px 25px;
    }

    .dashboard-title {
      font-size: 2.2rem;
    }

    .stats-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .admin-actions {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .action-card {
      flex-direction: column;
      text-align: center;
      gap: 20px;
    }
  }
</style>
{% endblock %}

{% block title %}Dashboard Admin{% endblock %}

{% block content %}
<div class="dashboard-container">
  <div class="dashboard-header">
    <h1 class="dashboard-title">
      <i class="fas fa-tachometer-alt"></i>
      Dashboard Administrateur
    </h1>
    <p class="dashboard-subtitle">Bienvenue, {{ request.user.username }}! Gérez votre plateforme d'apprentissage en ligne.</p>
  </div>

  <div class="stats-grid">
    <div class="stat-card students">
      <div class="stat-header">
        <div class="stat-icon">
          <i class="fas fa-user-graduate"></i>
        </div>
        <h3 class="stat-title">Étudiants</h3>
      </div>
      <div class="stat-number">{{ student_count }}</div>
    </div>

    <div class="stat-card courses">
      <div class="stat-header">
        <div class="stat-icon">
          <i class="fas fa-book"></i>
        </div>
        <h3 class="stat-title">Cours</h3>
      </div>
      <div class="stat-number">{{ course_count }}</div>
    </div>

    <div class="stat-card instructors">
      <div class="stat-header">
        <div class="stat-icon">
          <i class="fas fa-chalkboard-teacher"></i>
        </div>
        <h3 class="stat-title">Instructeurs</h3>
      </div>
      <div class="stat-number">{{ instructor_count }}</div>
    </div>

    <div class="stat-card modules">
      <div class="stat-header">
        <div class="stat-icon">
          <i class="fas fa-cubes"></i>
        </div>
        <h3 class="stat-title">Modules</h3>
      </div>
      <div class="stat-number">{{ module_count }}</div>
    </div>
  </div>

  <!-- Actions d'administration modernes -->
  <div class="admin-actions">
    <div class="action-card">
      <div class="action-icon users">
        <i class="fas fa-users"></i>
      </div>
      <div class="action-content">
        <h3>Gestion des Utilisateurs</h3>
        <p>Gérez les comptes utilisateurs, ajoutez des instructeurs et des étudiants</p>
        <a href="{% url 'list_users' %}" class="btn-action">
          <i class="fas fa-users"></i> Gérer les utilisateurs
        </a>
      </div>
    </div>

    <div class="action-card">
      <div class="action-icon courses">
        <i class="fas fa-book"></i>
      </div>
      <div class="action-content">
        <h3>Gestion des Cours</h3>
        <p>Créez, modifiez et supprimez des cours sur la plateforme</p>
        <a href="{% url 'course_list' %}" class="btn-action">
          <i class="fas fa-book"></i> Gérer les cours
        </a>
      </div>
    </div>



    <div class="action-card">
      <div class="action-icon instructors">
        <i class="fas fa-chalkboard-teacher"></i>
      </div>
      <div class="action-content">
        <h3>Ajouter un Instructeur</h3>
        <p>Ajoutez de nouveaux instructeurs pour créer et gérer des cours</p>
        <a href="{% url 'add_instructor' %}" class="btn-action">
          <i class="fas fa-user-plus"></i> Ajouter un instructeur
        </a>
      </div>
    </div>
  </div>
</div>
{% endblock %}
