{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/admin_style.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block title %}Dashboard Admin{% endblock %}

{% block content %}
<div class="admin-container">
  <div class="admin-header">
    <h1><i class="fas fa-tachometer-alt"></i> Dashboard Administrateur</h1>
    <p>Bienvenue, {{ request.user.username }}! Gérez votre plateforme d'apprentissage en ligne.</p>
  </div>

  <div class="admin-stats">
    <div class="stat-card students position-relative">
      <h3><i class="fas fa-user-graduate"></i> Étudiants</h3>
      <div class="number">{{ student_count }}</div>
      <div class="icon"><i class="fas fa-user-graduate"></i></div>
    </div>
    
    <div class="stat-card courses position-relative">
      <h3><i class="fas fa-book"></i> Cours</h3>
      <div class="number">{{ course_count }}</div>
      <div class="icon"><i class="fas fa-book"></i></div>
    </div>
    
    <div class="stat-card instructors position-relative">
      <h3><i class="fas fa-chalkboard-teacher"></i> Instructeurs</h3>
      <div class="number">{{ instructor_count }}</div>
      <div class="icon"><i class="fas fa-chalkboard-teacher"></i></div>
    </div>
    
    <div class="stat-card modules position-relative">
      <h3><i class="fas fa-cubes"></i> Modules</h3>
      <div class="number">{{ module_count }}</div>
      <div class="icon"><i class="fas fa-cubes"></i></div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-6">
      <div class="admin-form">
        <h2><i class="fas fa-chart-pie"></i> Répartition des utilisateurs</h2>
        <canvas id="userChart" width="400" height="300"></canvas>
      </div>
    </div>
    <div class="col-md-6">
      <div class="admin-form">
        <h2><i class="fas fa-chart-line"></i> Inscriptions mensuelles</h2>
        <canvas id="enrollmentChart" width="400" height="300"></canvas>
      </div>
    </div>
  </div>

  <div class="admin-actions">
    <div class="action-card">
      <h3><i class="fas fa-users"></i> Gestion des Utilisateurs</h3>
      <p>Gérez les comptes utilisateurs, ajoutez des instructeurs et des étudiants.</p>
      <a href="{% url 'list_users' %}" class="btn-admin users">
        <i class="fas fa-users"></i> Gérer les utilisateurs
      </a>
    </div>
    
    <div class="action-card">
      <h3><i class="fas fa-book"></i> Gestion des Cours</h3>
      <p>Créez, modifiez et supprimez des cours sur la plateforme.</p>
      <a href="{% url 'course_list' %}" class="btn-admin courses">
        <i class="fas fa-book"></i> Gérer les cours
      </a>
    </div>
    
    <div class="action-card">
      <h3><i class="fas fa-chalkboard-teacher"></i> Ajouter un Instructeur</h3>
      <p>Ajoutez de nouveaux instructeurs pour créer et gérer des cours.</p>
      <a href="{% url 'add_instructor' %}" class="btn-admin instructors">
        <i class="fas fa-user-plus"></i> Ajouter un instructeur
      </a>
    </div>
    
    <div class="action-card">
      <h3><i class="fas fa-chart-line"></i> Statistiques</h3>
      <p>Consultez les statistiques détaillées de votre plateforme.</p>
      <a href="#" class="btn-admin modules">
        <i class="fas fa-chart-line"></i> Voir les statistiques
      </a>
    </div>
  </div>

  <div class="admin-form">
    <h2><i class="fas fa-bell"></i> Dernières Activités</h2>
    <table class="admin-table">
      <thead>
        <tr>
          <th>Utilisateur</th>
          <th>Action</th>
          <th>Date</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>user123</td>
          <td>S'est inscrit au cours "Python pour débutants"</td>
          <td>Il y a 2 heures</td>
        </tr>
        <tr>
          <td>instructor456</td>
          <td>A créé un nouveau module "Introduction à Django"</td>
          <td>Il y a 5 heures</td>
        </tr>
        <tr>
          <td>admin</td>
          <td>A ajouté un nouvel instructeur</td>
          <td>Il y a 1 jour</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'js/admin.js' %}"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Graphique de répartition des utilisateurs
    const userCtx = document.getElementById('userChart').getContext('2d');
    const userChart = new Chart(userCtx, {
      type: 'doughnut',
      data: {
        labels: ['Étudiants', 'Instructeurs', 'Administrateurs'],
        datasets: [{
          data: [ { student_count }, { instructor_count }, 1],
          backgroundColor: [
            '#4cc9f0',
            '#f72585',
            '#4361ee'
          ],
          borderWidth: 0
        }]
      },
      options :{
        responsive: true,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });
    
    // Graphique d'inscriptions mensuelles
    const enrollmentCtx = document.getElementById('enrollmentChart').getContext('2d');
    const enrollmentChart = new Chart(enrollmentCtx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin'],
        datasets: [{
          label: 'Inscriptions',
          data: [12, 19, 3, 5, 2, 3],
          backgroundColor: 'rgba(67, 97, 238, 0.2)',
          borderColor: 'rgba(67, 97, 238, 1)',
          borderWidth: 2,
          tension: 0.3,
          fill: true
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  });
</script>
{% endblock %}
