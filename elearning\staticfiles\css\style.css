/* Variables globales */
:root {
  --primary-color: #4361ee;
  --secondary-color: #3a0ca3;
  --accent-color: #f72585;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --success-color: #4cc9f0;
  --warning-color: #f8961e;
  --danger-color: #f94144;
  --border-radius: 0.5rem;
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
}

/* Styles globaux */
body {
  font-family: 'Inter', sans-serif;
  color: var(--dark-color);
  line-height: 1.6;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Composants personnalisés */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.card-hover {
  transition: var(--transition);
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow);
}

/* Styles responsifs */
@media (max-width: 768px) {
  .display-4 {
    font-size: 2.5rem;
  }
  
  .lead {
    font-size: 1rem;
  }
}
.btn-outline-purple {
  color: #6f42c1;
  border-color: #6f42c1;
}
.btn-outline-purple:hover {
  background-color: #6f42c1;
  color: white;
}
.btn-purple {
  background-color: #6f42c1;
  border: 1px solid #6f42c1;
  color: #fff;
  transition: background-color 0.3s ease;
}

.btn-purple:hover {
  background-color: #5a32a3;
  border-color: #5a32a3;
  color: #fff;
}

