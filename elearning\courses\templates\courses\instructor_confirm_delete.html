{% extends 'base.html' %}
{% load static %}

{% block title %}Supprimer {{ course.title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .delete-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .delete-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .warning-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #fff3cd;
  }

  .delete-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
  }

  .course-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 15px;
    margin: 2rem 0;
    text-align: left;
  }

  .course-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .course-meta {
    color: #666;
    font-size: 0.9rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .warning-list {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 2rem 0;
    text-align: left;
  }

  .warning-list h5 {
    color: #856404;
    margin-bottom: 1rem;
  }

  .warning-list ul {
    color: #856404;
    margin: 0;
    padding-left: 1.5rem;
  }

  .warning-list li {
    margin-bottom: 0.5rem;
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
  }

  .btn-cancel {
    background: #6c757d;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
  }

  .btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
  }

  .btn-delete {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
  }

  .btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
  }

  .confirmation-text {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
  <div class="delete-header">
    <div class="warning-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    <h1>Supprimer le Cours</h1>
    <p>Cette action est irréversible</p>
  </div>

  <div class="delete-card">
    <p class="confirmation-text">
      Êtes-vous absolument sûr de vouloir supprimer ce cours ? 
      Cette action ne peut pas être annulée.
    </p>

    <!-- Informations du cours -->
    <div class="course-info">
      <div class="course-title">{{ course.title }}</div>
      <p class="text-muted">{{ course.description|truncatewords:20 }}</p>
      
      <div class="course-meta">
        <div class="meta-item">
          <i class="fas fa-users"></i>
          <span>{{ course.students.count }} étudiants inscrits</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-list"></i>
          <span>{{ course.modules.count }} modules</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-calendar"></i>
          <span>Créé le {{ course.created_at|date:"d/m/Y" }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-tag"></i>
          <span>{{ course.get_category_display }}</span>
        </div>
      </div>
    </div>

    <!-- Avertissements -->
    <div class="warning-list">
      <h5><i class="fas fa-exclamation-triangle"></i> Conséquences de la suppression :</h5>
      <ul>
        <li>Le cours sera définitivement supprimé de la plateforme</li>
        <li>Tous les modules et leçons associés seront supprimés</li>
        <li>Les étudiants inscrits perdront l'accès au contenu</li>
        <li>L'historique de progression des étudiants sera perdu</li>
        <li>Les fichiers et ressources du cours seront supprimés</li>
        <li>Cette action ne peut pas être annulée</li>
      </ul>
    </div>

    <!-- Boutons d'action -->
    <div class="action-buttons">
      <a href="{% url 'instructor_course_detail' course.id %}" class="btn-cancel">
        <i class="fas fa-times"></i>
        Annuler
      </a>
      
      <form method="post" style="display: inline;">
        {% csrf_token %}
        <button type="submit" class="btn-delete" onclick="return confirmDelete()">
          <i class="fas fa-trash"></i>
          Supprimer Définitivement
        </button>
      </form>
    </div>
  </div>
</div>

<script>
function confirmDelete() {
    return confirm(
        'ATTENTION: Cette action est irréversible!\n\n' +
        'Le cours "{{ course.title }}" et tout son contenu seront définitivement supprimés.\n\n' +
        'Tapez "SUPPRIMER" pour confirmer:'
    );
}

// Double confirmation pour éviter les suppressions accidentelles
document.querySelector('.btn-delete').addEventListener('click', function(e) {
    e.preventDefault();
    
    const courseName = '{{ course.title }}';
    const confirmation = prompt(
        `Pour confirmer la suppression, tapez exactement le nom du cours:\n\n"${courseName}"`
    );
    
    if (confirmation === courseName) {
        if (confirm('Êtes-vous absolument certain de vouloir supprimer ce cours ?')) {
            this.closest('form').submit();
        }
    } else if (confirmation !== null) {
        alert('Le nom du cours ne correspond pas. Suppression annulée.');
    }
});
</script>
{% endblock %}
