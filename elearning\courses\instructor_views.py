from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.db.models import Count, Avg
from django.http import JsonResponse
from django.utils import timezone
from datetime import timedelta

from users.models import User
from .models import Course, Module, Lesson, Progress
from messaging.models import Message
from .forms import CourseForm, ModuleForm, LessonForm

def is_instructor(user):
    return user.is_authenticated and user.role == 'instructor'

@login_required
@user_passes_test(is_instructor)
def instructor_dashboard(request):
    """Dashboard principal pour les instructeurs"""
    instructor = request.user
    
    # Statistiques de l'instructeur
    my_courses = Course.objects.filter(instructor=instructor)
    total_courses = my_courses.count()
    published_courses = my_courses.filter(is_published=True).count()
    total_students = sum(course.students.count() for course in my_courses)
    total_lessons = sum(course.modules.aggregate(
        lesson_count=Count('lessons')
    )['lesson_count'] or 0 for course in my_courses)
    
    # Cours récents
    recent_courses = my_courses.order_by('-created_at')[:5]
    
    # Messages récents
    recent_messages = Message.objects.filter(
        recipient=instructor
    ).order_by('-created_at')[:5]
    
    # Activité récente (inscriptions cette semaine)
    week_ago = timezone.now() - timedelta(days=7)
    recent_enrollments = []
    for course in my_courses:
        enrollments = course.students.filter(
            enrollment__created_at__gte=week_ago
        ).count() if hasattr(course, 'enrollment') else 0
        if enrollments > 0:
            recent_enrollments.append({
                'course': course,
                'count': enrollments
            })
    
    context = {
        'total_courses': total_courses,
        'published_courses': published_courses,
        'total_students': total_students,
        'total_lessons': total_lessons,
        'recent_courses': recent_courses,
        'recent_messages': recent_messages,
        'recent_enrollments': recent_enrollments,
    }
    
    return render(request, 'courses/instructor_dashboard.html', context)

@login_required
@user_passes_test(is_instructor)
def my_courses(request):
    """Liste des cours de l'instructeur"""
    instructor = request.user
    courses = Course.objects.filter(instructor=instructor).order_by('-created_at')
    
    # Ajouter les statistiques pour chaque cours
    for course in courses:
        course.student_count = course.students.count()
        course.lesson_count = sum(module.lessons.count() for module in course.modules.all())
        course.completion_rate = 0  # À calculer plus tard avec le système de progression
    
    context = {
        'courses': courses,
    }
    
    return render(request, 'courses/instructor_courses.html', context)

@login_required
@user_passes_test(is_instructor)
def create_course(request):
    """Créer un nouveau cours"""
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES)
        if form.is_valid():
            course = form.save(commit=False)
            course.instructor = request.user
            course.save()
            messages.success(request, f"Le cours '{course.title}' a été créé avec succès!")
            return redirect('instructor_course_detail', course_id=course.id)
    else:
        form = CourseForm()
    
    context = {
        'form': form,
        'title': 'Créer un nouveau cours'
    }
    
    return render(request, 'courses/instructor_course_form.html', context)

@login_required
@user_passes_test(is_instructor)
def course_detail(request, course_id):
    """Détails d'un cours pour l'instructeur"""
    course = get_object_or_404(Course, id=course_id, instructor=request.user)
    modules = course.modules.all().order_by('order')
    
    # Statistiques du cours
    student_count = course.students.count()
    lesson_count = sum(module.lessons.count() for module in modules)
    
    context = {
        'course': course,
        'modules': modules,
        'student_count': student_count,
        'lesson_count': lesson_count,
    }
    
    return render(request, 'courses/instructor_course_detail.html', context)

@login_required
@user_passes_test(is_instructor)
def edit_course(request, course_id):
    """Modifier un cours"""
    course = get_object_or_404(Course, id=course_id, instructor=request.user)
    
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES, instance=course)
        if form.is_valid():
            form.save()
            messages.success(request, f"Le cours '{course.title}' a été mis à jour!")
            return redirect('instructor_course_detail', course_id=course.id)
    else:
        form = CourseForm(instance=course)
    
    context = {
        'form': form,
        'course': course,
        'title': f'Modifier {course.title}'
    }
    
    return render(request, 'courses/instructor_course_form.html', context)

@login_required
@user_passes_test(is_instructor)
def delete_course(request, course_id):
    """Supprimer un cours"""
    course = get_object_or_404(Course, id=course_id, instructor=request.user)
    
    if request.method == 'POST':
        course_title = course.title
        course.delete()
        messages.success(request, f"Le cours '{course_title}' a été supprimé.")
        return redirect('instructor_my_courses')
    
    context = {
        'course': course,
        'title': 'Supprimer le cours'
    }
    
    return render(request, 'courses/instructor_confirm_delete.html', context)

@login_required
@user_passes_test(is_instructor)
def course_students(request, course_id):
    """Liste des étudiants inscrits à un cours"""
    course = get_object_or_404(Course, id=course_id, instructor=request.user)
    students = course.students.all().order_by('username')
    
    # Ajouter les statistiques de progression pour chaque étudiant
    for student in students:
        student.progress_rate = course.get_completion_rate(student)
        student.completed_lessons = Progress.objects.filter(
            student=student,
            course=course,
            completed=True
        ).count()
    
    context = {
        'course': course,
        'students': students,
    }
    
    return render(request, 'courses/instructor_course_students.html', context)

@login_required
@user_passes_test(is_instructor)
def toggle_course_status(request, course_id):
    """Publier/dépublier un cours"""
    course = get_object_or_404(Course, id=course_id, instructor=request.user)
    
    if request.method == 'POST':
        course.is_published = not course.is_published
        course.save()
        
        status = "publié" if course.is_published else "dépublié"
        messages.success(request, f"Le cours '{course.title}' a été {status}.")
    
    return redirect('instructor_course_detail', course_id=course.id)
