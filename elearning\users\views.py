from django.shortcuts import render, redirect
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseForbidden
from .forms import LoginForm, CustomUserCreationForm, InstructorCreationForm, StudentCreationForm
from .models import User
from courses.models import Course
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import get_object_or_404


def register_view(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            return redirect('login')
    else:
        form = CustomUserCreationForm()
    return render(request, 'users/register.html', {'form': form})

def login_view(request):
    form = LoginForm(request.POST or None)
    error = None
    if request.method == 'POST':
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            user = authenticate(request, username=username, password=password)
            if user:
                login(request, user)
                if user.role == 'admin':
                    return redirect('dashboard')
                elif user.role == 'instructor':
                    return redirect('instructor_dashboard')
                else:
                    return redirect('course_list')
            else:
                error = "Identifiants invalides. Veuillez réessayer."
    return render(request, 'users/login.html', {'form': form, 'error': error})

def logout_view(request):
    logout(request)
    return redirect('login')

@login_required
def admin_dashboard(request):
    if not request.user.role == 'admin':
        return HttpResponseForbidden("Vous n'êtes pas autorisé.")
    courses = Course.objects.all()
    return render(request, 'users/admin_dashboard.html', {'courses': courses})

@login_required
def profile_view(request):
    return render(request, 'users/profile.html')

@login_required
def add_instructor_view(request):
    if not request.user.role == 'admin':
        return HttpResponseForbidden()
    
    if request.method == 'POST':
        form = InstructorCreationForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "✅ Instructeur ajouté avec succès.")
            return redirect('dashboard')
    else:
        form = InstructorCreationForm()

    return render(request, 'users/add_instructor.html', {'form': form})

@login_required
def add_instructor(request):
    if not request.user.role == 'admin':
        return HttpResponseForbidden("Non autorisé.")

    form = InstructorCreationForm(request.POST or None)
    if request.method == 'POST' and form.is_valid():
        form.save()
        messages.success(request, "✅ Instructeur ajouté avec succès.")
        return redirect('dashboard')

    return render(request, 'users/add_instructor.html', {'form': form})

@login_required
def user_list(request):
    if request.user.role != 'admin':
        return HttpResponseForbidden("Accès non autorisé.")

    query = request.GET.get('q', '')
    role_filter = request.GET.get('role', '')

    users = User.objects.exclude(role='admin')

    if query:
        users = users.filter(Q(username__icontains=query) | Q(email__icontains=query))

    if role_filter in ['student', 'instructor']:
        users = users.filter(role=role_filter)

    paginator = Paginator(users.order_by('-date_joined'), 8)
    page = request.GET.get('page')
    users_page = paginator.get_page(page)

    return render(request, 'users/user_list.html', {
        'users': users_page,
        'query': query,
        'role_filter': role_filter,
    })
@login_required
def delete_user(request, user_id):
    user = get_object_or_404(User, id=user_id)
    
    # Vérifiez que l'utilisateur actuel a les permissions nécessaires
    if not (request.user.is_superuser or request.user.role == 'admin'):
        messages.error(request, "Vous n'avez pas les permissions nécessaires pour effectuer cette action.")
        return redirect('list_users')
    
    if request.method == 'POST':
        # Si c'est une requête POST, supprimez l'utilisateur
        username = user.username  # Sauvegardez le nom avant de supprimer
        user.delete()
        messages.success(request, f"L'utilisateur {username} a été supprimé avec succès.")
        return redirect('list_users')
    
    # Si c'est une requête GET, affichez la page de confirmation
    return render(request, 'users/confirm_delete_user.html', {'user': user})

@login_required
def create_user(request):
    if not request.user.role == 'admin':
        return HttpResponseForbidden("Vous n'êtes pas autorisé.")
    
    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        role = request.POST.get('role')
        
        if username and email and password and role:
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password
            )
            user.role = role
            user.save()
            
            messages.success(request, f"✅ Utilisateur {username} ajouté avec succès.")
            return redirect('list_users')
        else:
            messages.error(request, "❌ Veuillez remplir tous les champs.")
    
    return redirect('list_users')

@login_required
def add_user(request):
    if not request.user.role == 'admin':
        return HttpResponseForbidden("Vous n'êtes pas autorisé.")

    if request.method == 'POST':
        form = StudentCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, f"✅ Utilisateur {user.username} ajouté avec succès.")
            return redirect('list_users')
        else:
            messages.error(request, "❌ Erreur lors de la création de l'utilisateur.")
    else:
        form = StudentCreationForm()

    return render(request, 'users/add_user.html', {'form': form})
