{% extends 'base.html' %}
{% load static %}

{% block title %}{{ course.title }}{% endblock %}

{% block extra_css %}
<style>
  /* Style moderne et attrayant pour l'en-tête du cours */
  .course-header, .course-banner, .course-hero {
    background: linear-gradient(135deg, #ffd6e0, #c8e7ff) !important;
    color: #333 !important;
    padding: 25px;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  /* Améliorer la lisibilité du texte */
  .course-title, .course-header h1, .course-header h2, .course-description {
    color: #333 !important;
    font-weight: 600;
    position: relative;
    z-index: 2;
  }
  
  /* Ajouter des éléments décoratifs modernes */
  .course-header::after {
    content: '';
    position: absolute;
    bottom: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    z-index: 1;
  }
  
  /* Améliorer l'apparence des badges et étiquettes */
  .badge, .tag, .level-badge {
    background: rgba(255, 255, 255, 0.25) !important;
    color: #333 !important;
    border: 1px solid rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(5px);
    padding: 5px 12px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.85rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }
  
  /* Styles pour les champs de formulaire */
  input[type="text"], 
  input[type="number"], 
  textarea, 
  select {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 1rem;
    transition: all 0.3s;
    background: #f8f9fa;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.03);
    width: 100%;
    margin-bottom: 20px;
  }
  
  input[type="text"]:hover, 
  input[type="number"]:hover, 
  textarea:hover, 
  select:hover {
    border-color: #ced4da;
    background: #fff;
  }
  
  input[type="text"]:focus, 
  input[type="number"]:focus, 
  textarea:focus, 
  select:focus {
    border-color: #6930c3;
    box-shadow: 0 0 0 3px rgba(105, 48, 195, 0.2);
    background: #fff;
    outline: none;
  }
  
  label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
  }
  
  select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236930c3' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
  }
  
  input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    accent-color: #6930c3;
  }
  
  button, .btn, input[type="submit"] {
    padding: 14px 30px;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s;
    cursor: pointer;
    background: linear-gradient(135deg, #5e60ce, #6930c3);
    color: white;
    box-shadow: 0 5px 15px rgba(105, 48, 195, 0.3);
  }
  
  button:hover, .btn:hover, input[type="submit"]:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(105, 48, 195, 0.4);
  }
  
  .btn-secondary, button[type="reset"] {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
  }
  
  .btn-secondary:hover, button[type="reset"]:hover {
    background: #e9ecef;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  /* Style pour le sélecteur de fichier */
  input[type="file"] {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px dashed #ced4da;
    border-radius: 12px;
    padding: 20px;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
  }
  
  input[type="file"]:hover {
    border-color: #6930c3;
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
  }
  
  /* Suppression du double titre */
  .form-header h1, .form-header h2, .form-header .form-title {
    display: none;
  }
  
  /* Style pour le conteneur de formulaire */
  .form-container {
    max-width: 1000px;
    margin: 40px auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  /* En-tête du formulaire avec dégradé */
  .form-header {
    background: linear-gradient(135deg, #5e60ce, #6930c3, #7400b8);
    color: white;
    padding: 25px 30px;
    position: relative;
    overflow: hidden;
  }
  
  /* Corps du formulaire */
  .form-body {
    padding: 30px;
  }
  
  /* Conteneur de boutons */
  .button-container {
    display: flex;
    gap: 15px;
    margin-top: 30px;
  }
  
  .course-detail-container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;
  }
  
  .course-header {
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.9), rgba(58, 12, 163, 0.9));
    border-radius: 15px;
    padding: 40px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
  }
  
  .course-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('{% static "img/pattern.svg" %}') no-repeat;
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
  }
  
  .course-header-content {
    position: relative;
    z-index: 1;
  }
  
  .course-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .course-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .meta-item {
    display: flex;
    align-items: center;
    font-size: 1rem;
  }
  
  .meta-item i {
    margin-right: 8px;
    font-size: 1.2rem;
  }
  
  .course-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 20px;
    max-width: 800px;
  }
  
  .course-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
  }
  
  .course-main {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }
  
  .course-sidebar {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    align-self: start;
  }
  
  .section-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #333;
    position: relative;
    padding-bottom: 10px;
  }
  
  .section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 4px;
    background: linear-gradient(to right, #4361ee, #3a0ca3);
    border-radius: 2px;
  }
  
  .learning-objectives {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
  }
  
  .objective-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .objective-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
  }
  
  .objective-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
  
  .objective-icon {
    background: rgba(67, 97, 238, 0.1);
    color: #4361ee;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
  }
  
  .objective-text {
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
  }
  
  .modules-list {
    padding: 0;
    margin: 0;
    list-style: none;
  }
  
  .module-item {
    background: white;
    border-radius: 10px;
    margin-bottom: 15px;
    transition: all 0.3s;
    border: 1px solid #e9ecef;
    overflow: hidden;
  }
  
  .module-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  }
  
  .module-header {
    padding: 15px 20px;
    background: #f8f9fa;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    color: #333;
  }
  
  .module-content {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
  }
  
  .lesson-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .lesson-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f1f1;
  }
  
  .lesson-item:last-child {
    border-bottom: none;
  }
  
  .lesson-icon {
    margin-right: 10px;
    color: #4361ee;
  }
  
  .lesson-title {
    flex-grow: 1;
    font-size: 0.95rem;
  }
  
  .lesson-duration {
    font-size: 0.85rem;
    color: #6c757d;
  }
  
  .course-info-card {
    padding: 25px;
    text-align: center;
  }
  
  .price-tag {
    font-size: 2.5rem;
    font-weight: 800;
    color: #333;
    margin-bottom: 20px;
  }
  
  .price-tag.free {
    color: #4361ee;
  }
  
  .enrollment-btn {
    display: block;
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4361ee, #3a0ca3);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 15px;
    transition: all 0.3s;
    text-decoration: none;
    text-align: center;
  }
  
  .enrollment-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
    color: white;
  }
  
  .admin-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
  }
  
  .admin-btn {
    flex: 1;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s;
    text-decoration: none;
  }
  
  .edit-btn {
    background: #f8961e;
    color: white;
  }
  
  .edit-btn:hover {
    background: #e67e00;
    color: white;
  }
  
  .delete-btn {
    background: #f94144;
    color: white;
  }
  
  .delete-btn:hover {
    background: #e71d36;
    color: white;
  }
  
  .instructor-info {
    display: flex;
    align-items: center;
    padding: 20px;
    border-top: 1px solid #e9ecef;
  }
  
  .instructor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
  }
  
  .instructor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .instructor-details {
    flex-grow: 1;
  }
  
  .instructor-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
  }
  
  .instructor-role {
    font-size: 0.9rem;
    color: #6c757d;
  }
  
  .course-stats {
    display: flex;
    border-top: 1px solid #e9ecef;
  }
  
  .stat-item {
    flex: 1;
    padding: 15px;
    text-align: center;
    border-right: 1px solid #e9ecef;
  }
  
  .stat-item:last-child {
    border-right: none;
  }
  
  .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4361ee;
    margin-bottom: 5px;
  }
  
  .stat-label {
    font-size: 0.85rem;
    color: #6c757d;
  }
  
  @media (max-width: 768px) {
    .course-content {
      grid-template-columns: 1fr;
    }
    
    .course-title {
      font-size: 2rem;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="course-detail-container">
  <div class="course-header">
    <div class="course-header-content">
      <h1 class="course-title">{{ course.title }}</h1>
      
      <div class="course-meta">
        <div class="meta-item">
          <i class="fas fa-user"></i>
          <span>{{ course.instructor.username }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-clock"></i>
          <span>{{ course.duration }} heures</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-signal"></i>
          <span>{{ course.get_level_display }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-tag"></i>
          <span>{{ course.get_category_display }}</span>
        </div>
      </div>
      
      <p class="course-description">{{ course.description }}</p>
    </div>
  </div>
  
  <div class="course-content">
    <div class="course-main">
      <div class="p-4">
        <div class="learning-objectives">
          <h3 class="section-title">Ce que vous apprendrez</h3>
          
          {% if course.objectives.all %}
            <ul class="objective-list">
              {% for objective in course.objectives.all %}
                <li class="objective-item">
                  <div class="objective-icon">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="objective-text">{{ objective.description }}</div>
                </li>
              {% endfor %}
            </ul>
          {% else %}
            <p class="objective-item">
              <div class="objective-icon">
                <i class="fas fa-info"></i>
              </div>
              <div class="objective-text">Aucun objectif d'apprentissage défini pour ce cours.</div>
            </p>
          {% endif %}
        </div>
        
        <h3 class="section-title">Modules du cours</h3>
        
        {% if modules %}
          <ul class="modules-list">
            {% for module in modules %}
              <li class="module-item">
                <div class="module-header" onclick="toggleModule(this)">
                  <span>{{ module.title }}</span>
                  <i class="fas fa-chevron-down"></i>
                </div>
                <div class="module-content" style="display: none;">
                  {% if module.lessons.all %}
                    <ul class="lesson-list">
                      {% for lesson in module.lessons.all %}
                        <li class="lesson-item">
                          <div class="lesson-icon">
                            <i class="fas fa-play-circle"></i>
                          </div>
                          <div class="lesson-title">{{ lesson.title }}</div>
                          <div class="lesson-duration">{{ lesson.duration }} min</div>
                        </li>
                      {% endfor %}
                    </ul>
                  {% else %}
                    <p class="text-muted">Aucune leçon dans ce module.</p>
                  {% endif %}
                </div>
              </li>
            {% endfor %}
          </ul>
        {% else %}
          <div class="text-center p-5">
            <i class="fas fa-book fa-3x text-muted mb-3"></i>
            <h5>Aucun module disponible</h5>
            <p class="text-muted">Ce cours n'a pas encore de modules.</p>
          </div>
        {% endif %}
      </div>
    </div>
    
    <div class="course-sidebar">
      <div class="course-info-card">
        <div class="price-tag free">
          <i class="fas fa-book-open"></i> Gratuit
        </div>
        
        <a href="{% url 'enroll_course' course.id %}" class="enrollment-btn">
          <i class="fas fa-graduation-cap me-2"></i>S'inscrire au cours
        </a>
        
        {% if user.is_staff or user.is_superuser %}
          <div class="admin-actions">
            <a href="{% url 'edit_course' course.id %}" class="admin-btn edit-btn">
              <i class="fas fa-edit me-1"></i> Modifier
            </a>
            <a href="{% url 'delete_course' course.id %}" class="admin-btn delete-btn">
              <i class="fas fa-trash-alt me-1"></i> Supprimer
            </a>
          </div>
        {% endif %}
      </div>
      
      <div class="instructor-info">
        <div class="instructor-avatar">
          {% if course.instructor.profile.avatar %}
            <img src="{{ course.instructor.profile.avatar.url }}" alt="{{ course.instructor.username }}">
          {% else %}
            <i class="fas fa-user"></i>
          {% endif %}
        </div>
        <div class="instructor-details">
          <div class="instructor-name">{{ course.instructor.username }}</div>
          <div class="instructor-role">Instructeur</div>
        </div>
      </div>
      
      <div class="course-stats">
        <div class="stat-item">
          <div class="stat-value">{{ course.enrolled_students.count }}</div>
          <div class="stat-label">Étudiants</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ modules|length }}</div>
          <div class="stat-label">Modules</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ course.duration }}</div>
          <div class="stat-label">Heures</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function toggleModule(element) {
    const content = element.nextElementSibling;
    const icon = element.querySelector('i');
    
    if (content.style.display === 'none') {
      content.style.display = 'block';
      icon.classList.remove('fa-chevron-down');
      icon.classList.add('fa-chevron-up');
    } else {
      content.style.display = 'none';
      icon.classList.remove('fa-chevron-up');
      icon.classList.add('fa-chevron-down');
    }
  }
</script>
{% endblock %}
