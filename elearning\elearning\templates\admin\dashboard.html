<div class="admin-header">
  <div class="container-fluid">
    <div class="row align-items-center">
      <div class="col-md-6">
        <h1 class="admin-title">
          <i class="fas fa-tachometer-alt me-2"></i>
          Dashboard Administrateur
        </h1>
        <p class="admin-welcome">Bienvenue, {{ request.user.username }}! Gérez votre plateforme d'apprentissage en ligne.</p>
      </div>
      <div class="col-md-6">
        <div class="admin-nav">
          <a href="{% url 'course_list' %}" class="admin-nav-btn">
            <i class="fas fa-home"></i> Accueil
          </a>
          <a href="{% url 'admin_users' %}" class="admin-nav-btn">
            <i class="fas fa-users"></i> Utilisateurs
          </a>
          <a href="{% url 'admin_courses' %}" class="admin-nav-btn">
            <i class="fas fa-book"></i> Cours
          </a>
          <a href="{% url 'admin_settings' %}" class="admin-nav-btn">
            <i class="fas fa-cog"></i> Paramètres
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Section des graphiques -->
<div class="row mt-4">
  <!-- Répartition des utilisateurs -->
  <div class="col-lg-6 mb-4">
    <div class="card shadow-sm h-100">
      <div class="card-header bg-white">
        <h5 class="mb-0"><i class="fas fa-chart-pie text-primary me-2"></i>Répartition des utilisateurs</h5>
      </div>
      <div class="card-body">
        <div class="chart-container" style="position: relative; height:300px;">
          <canvas id="userDistributionChart"></canvas>
        </div>
        <div class="d-none">
          <span data-student-count="{{ student_count }}"></span>
          <span data-instructor-count="{{ instructor_count }}"></span>
          <span data-admin-count="{{ admin_count|default:1 }}"></span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Inscriptions mensuelles -->
  <div class="col-lg-6 mb-4">
    <div class="card shadow-sm h-100">
      <div class="card-header bg-white">
        <h5 class="mb-0"><i class="fas fa-chart-line text-primary me-2"></i>Inscriptions mensuelles</h5>
      </div>
      <div class="card-body">
        <div class="chart-container" style="position: relative; height:300px;">
          <canvas id="monthlyRegistrationsChart"></canvas>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Progression des cours -->
  <div class="col-lg-6 mb-4">
    <div class="card shadow-sm h-100">
      <div class="card-header bg-white">
        <h5 class="mb-0"><i class="fas fa-chart-bar text-primary me-2"></i>Progression des cours</h5>
      </div>
      <div class="card-body">
        <div class="chart-container" style="position: relative; height:300px;">
          <canvas id="courseProgressChart"></canvas>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Activité hebdomadaire -->
  <div class="col-lg-6 mb-4">
    <div class="card shadow-sm h-100">
      <div class="card-header bg-white">
        <h5 class="mb-0"><i class="fas fa-chart-area text-primary me-2"></i>Activité hebdomadaire</h5>
      </div>
      <div class="card-body">
        <div class="chart-container" style="position: relative; height:300px;">
          <canvas id="weeklyActivityChart"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>


