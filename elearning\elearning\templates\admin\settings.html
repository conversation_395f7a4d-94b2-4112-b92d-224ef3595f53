{% extends 'base.html' %}
{% load static %}

{% block title %}Paramètres | E-Learn+{% endblock %}

{% block content %}
<div class="container">
  <div class="admin-header">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-6">
          <h1 class="admin-title">
            <i class="fas fa-cog me-2"></i>
            Paramètres
          </h1>
          <p class="admin-welcome">Configurez les paramètres de votre plateforme d'apprentissage.</p>
        </div>
        <div class="col-md-6">
          <div class="admin-nav">
            <a href="{% url 'dashboard' %}" class="admin-nav-btn">
              <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{% url 'admin_users' %}" class="admin-nav-btn">
              <i class="fas fa-users"></i> Utilisateurs
            </a>
            <a href="{% url 'admin_courses' %}" class="admin-nav-btn">
              <i class="fas fa-book"></i> Cours
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header">
          <h5 class="mb-0">Paramètres généraux</h5>
        </div>
        <div class="card-body">
          <form method="post">
            {% csrf_token %}
            <div class="mb-3">
              <label for="site_name" class="form-label">Nom du site</label>
              <input type="text" class="form-control" id="site_name" value="E-Learn+" name="site_name">
            </div>
            <div class="mb-3">
              <label for="site_description" class="form-label">Description</label>
              <textarea class="form-control" id="site_description" name="site_description" rows="3">Plateforme d'apprentissage en ligne</textarea>
            </div>
            <button type="submit" class="btn btn-primary">Enregistrer</button>
          </form>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header">
          <h5 class="mb-0">Paramètres d'email</h5>
        </div>
        <div class="card-body">
          <form method="post">
            {% csrf_token %}
            <div class="mb-3">
              <label for="email_host" class="form-label">Serveur SMTP</label>
              <input type="text" class="form-control" id="email_host" name="email_host">
            </div>
            <div class="mb-3">
              <label for="email_port" class="form-label">Port</label>
              <input type="number" class="form-control" id="email_port" name="email_port">
            </div>
            <div class="mb-3">
              <label for="email_user" class="form-label">Utilisateur</label>
              <input type="text" class="form-control" id="email_user" name="email_user">
            </div>
            <div class="mb-3">
              <label for="email_password" class="form-label">Mot de passe</label>
              <input type="password" class="form-control" id="email_password" name="email_password">
            </div>
            <button type="submit" class="btn btn-primary">Enregistrer</button>
          </form>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header">
          <h5 class="mb-0">Paramètres avancés</h5>
        </div>
        <div class="card-body">
          <form method="post">
            {% csrf_token %}
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="maintenance_mode" name="maintenance_mode">
              <label class="form-check-label" for="maintenance_mode">Mode maintenance</label>
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="allow_registration" name="allow_registration" checked>
              <label class="form-check-label" for="allow_registration">Autoriser les inscriptions</label>
            </div>
            <div class="mb-3">
              <label for="max_upload_size" class="form-label">Taille max. upload (MB)</label>
              <input type="number" class="form-control" id="max_upload_size" name="max_upload_size" value="10">
            </div>
            <button type="submit" class="btn btn-primary">Enregistrer</button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}