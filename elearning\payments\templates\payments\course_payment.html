{% extends 'base.html' %}
{% load static %}

{% block title %}Paiement - {{ course.title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .payment-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .payment-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: white;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .course-info {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .course-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 1rem;
  }

  .course-price {
    font-size: 2rem;
    font-weight: bold;
    color: #C8A8E9;
    text-align: center;
    margin: 1rem 0;
  }

  .payment-form {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
  }

  .form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
  }

  .form-control:focus {
    outline: none;
    border-color: #C8A8E9;
    box-shadow: 0 0 0 3px rgba(200, 168, 233, 0.1);
  }

  .payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .payment-method {
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
  }

  .payment-method:hover {
    border-color: #C8A8E9;
    transform: translateY(-2px);
  }

  .payment-method.selected {
    border-color: #C8A8E9;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: white;
  }

  .payment-method i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .btn-pay {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-pay:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(200, 168, 233, 0.3);
  }

  .security-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    text-align: center;
    color: #666;
    font-size: 0.9rem;
  }

  .course-features {
    list-style: none;
    padding: 0;
  }

  .course-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .course-features li:last-child {
    border-bottom: none;
  }

  .course-features i {
    color: #C8A8E9;
  }
</style>
{% endblock %}

{% block content %}
<div class="payment-container">
  <div class="payment-header">
    <h1><i class="fas fa-credit-card"></i> Paiement du Cours</h1>
    <p>Finalisez votre inscription</p>
  </div>

  <div class="course-info">
    <div class="course-title">{{ course.title }}</div>
    <p class="text-muted">{{ course.description|truncatewords:30 }}</p>
    
    <ul class="course-features">
      <li><i class="fas fa-play-circle"></i> {{ course.get_total_lessons }} leçons</li>
      <li><i class="fas fa-clock"></i> {{ course.duration }}</li>
      <li><i class="fas fa-certificate"></i> Certificat de completion</li>
      <li><i class="fas fa-mobile-alt"></i> Accès mobile et desktop</li>
      <li><i class="fas fa-infinity"></i> Accès à vie</li>
    </ul>
    
    <div class="course-price">{{ course.price }}€</div>
  </div>

  <div class="payment-form">
    <form method="post" action="{% url 'payments:process_course_payment' course.id %}">
      {% csrf_token %}
      
      <h3 class="mb-3">Choisissez votre méthode de paiement</h3>
      
      <div class="payment-methods">
        <div class="payment-method selected" data-method="card">
          <i class="fas fa-credit-card"></i>
          <div>Carte bancaire</div>
          <small>Visa, Mastercard, Amex</small>
        </div>
        <div class="payment-method" data-method="paypal">
          <i class="fab fa-paypal"></i>
          <div>PayPal</div>
          <small>Paiement sécurisé</small>
        </div>
        <div class="payment-method" data-method="bank_transfer">
          <i class="fas fa-university"></i>
          <div>Virement</div>
          <small>Virement bancaire</small>
        </div>
      </div>

      <input type="hidden" name="payment_method" id="payment_method" value="card">

      <div id="card-form" class="payment-details">
        <div class="form-group">
          <label class="form-label">Numéro de carte</label>
          <input type="text" class="form-control" placeholder="1234 5678 9012 3456" maxlength="19">
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Date d'expiration</label>
              <input type="text" class="form-control" placeholder="MM/AA" maxlength="5">
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">CVV</label>
              <input type="text" class="form-control" placeholder="123" maxlength="4">
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">Nom sur la carte</label>
          <input type="text" class="form-control" placeholder="Jean Dupont">
        </div>
      </div>

      <button type="submit" class="btn-pay">
        <i class="fas fa-lock"></i> Payer {{ course.price }}€
      </button>
      
      <div class="security-info">
        <i class="fas fa-shield-alt"></i>
        Paiement 100% sécurisé - Vos données sont protégées
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('.payment-method');
    const paymentMethodInput = document.getElementById('payment_method');
    
    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            // Retirer la sélection de tous les éléments
            paymentMethods.forEach(m => m.classList.remove('selected'));
            
            // Ajouter la sélection à l'élément cliqué
            this.classList.add('selected');
            
            // Mettre à jour la valeur cachée
            paymentMethodInput.value = this.dataset.method;
        });
    });
    
    // Formatage du numéro de carte
    const cardInput = document.querySelector('input[placeholder="1234 5678 9012 3456"]');
    if (cardInput) {
        cardInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '');
            let formattedValue = value.replace(/(.{4})/g, '$1 ').trim();
            e.target.value = formattedValue;
        });
    }
    
    // Formatage de la date d'expiration
    const expiryInput = document.querySelector('input[placeholder="MM/AA"]');
    if (expiryInput) {
        expiryInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            e.target.value = value;
        });
    }
});
</script>
{% endblock %}
