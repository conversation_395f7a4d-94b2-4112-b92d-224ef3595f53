{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container py-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-header">Découvrez nos cours</h1>
    {% if user.is_authenticated and user.role in 'admin,instructor' %}
    <a href="{% url 'add_course' %}" class="btn-add-course">
      <i class="fas fa-plus-circle"></i> Ajouter un cours
    </a>
    {% endif %}
  </div>

  <!-- Filtres et recherche -->
  <div class="row mb-4">
    <div class="col-md-6">
      <form method="get" class="search-form">
        <div class="input-group">
          <input type="text" name="search" class="form-control search-input" placeholder="Rechercher un cours..." value="{{ search_query }}">
          <button type="submit" class="btn search-btn"><i class="fas fa-search"></i></button>
        </div>
      </form>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
      <div class="d-flex gap-2">
        <select name="category" class="form-select filter-select">
          <option value="">Toutes les catégories</option>
          <option value="programming">Programmation</option>
          <option value="design">Design</option>
          <option value="business">Business</option>
        </select>
        <select name="level" class="form-select filter-select">
          <option value="">Tous les niveaux</option>
          <option value="beginner">Débutant</option>
          <option value="intermediate">Intermédiaire</option>
          <option value="advanced">Avancé</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Liste des cours -->
  <div class="course-list">
    {% for course in courses %}
    <div class="course-list-item">
      <div class="course-info">
        <h3 class="course-title">{{ course.title }}</h3>
        <p class="course-description">{{ course.description|default:"Pas de description disponible"|truncatechars:150 }}</p>
        <div class="course-meta">
          <span><i class="fas fa-user"></i> {{ course.instructor.username }}</span>
          <span><i class="fas fa-clock"></i> {{ course.duration }} heures</span>
          <span class="level-badge level-{{ course.level|default:'beginner' }}">
            <i class="fas fa-signal"></i> {{ course.get_level_display|default:"Débutant" }}
          </span>
        </div>
      </div>
      <div class="course-actions">
        <a href="{% url 'course_detail_by_id' course.id %}" class="btn-view">
          <i class="fas fa-eye"></i> Voir
        </a>
        {% if user.is_authenticated and user == course.instructor or user.is_staff %}
        <a href="{% url 'edit_course' course.id %}" class="btn-edit">
          <i class="fas fa-edit"></i> Modifier
        </a>
        <a href="{% url 'delete_course' course.id %}" class="btn-delete">
          <i class="fas fa-trash"></i> Supprimer
        </a>
        {% endif %}
      </div>
    </div>
    {% empty %}
    <div class="text-center py-5">
      <i class="fas fa-book-open fa-3x mb-3 text-muted"></i>
      <h3>Aucun cours trouvé</h3>
      <p>Essayez de modifier vos critères de recherche ou ajoutez un nouveau cours.</p>
    </div>
    {% endfor %}
  </div>
</div>
{% endblock %}
