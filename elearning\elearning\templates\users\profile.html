{% extends 'base.html' %}
{% load static %}
{% block title %}Mon Profil{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white text-center">
          <h4><i class="bi bi-person-circle me-2"></i> Mon Profil</h4>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-4 text-center">
              <img src="{% static 'img/user.png' %}" alt="Avatar" class="img-fluid rounded-circle" width="120">
            </div>
            <div class="col-md-8">
              <h5 class="mb-1">{{ user.username }}</h5>
              <p class="text-muted">{{ user.email }}</p>
              <span class="badge bg-info text-dark text-uppercase">{{ user.role }}</span>
            </div>
          </div>

          <hr>

          <p><strong>Date d'inscription :</strong> {{ user.date_joined|date:"d M Y à H:i" }}</p>
          <p><strong>Dernière connexion :</strong> {{ user.last_login|date:"d M Y à H:i" }}</p>

          <hr>
          <div class="d-flex justify-content-between">
            <a href="{% url 'course_list' %}" class="btn btn-secondary">← Retour aux cours</a>
            <a href="{% url 'logout' %}" class="btn btn-danger">Se déconnecter</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
