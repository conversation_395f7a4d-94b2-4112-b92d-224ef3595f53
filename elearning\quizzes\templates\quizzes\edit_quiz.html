{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
  .quiz-edit-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
  }

  .quiz-header {
    background: linear-gradient(135deg, #C8A8E9 0%, #A8D0F0 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    text-align: center;
  }

  .quiz-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .quiz-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1rem;
    flex-wrap: wrap;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
  }

  .tabs {
    display: flex;
    background: white;
    border-radius: 15px;
    padding: 0.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }

  .tab-button {
    flex: 1;
    padding: 1rem 2rem;
    border: none;
    background: transparent;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6B7280;
  }

  .tab-button.active {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(200, 168, 233, 0.3);
  }

  .tab-content {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }

  .tab-content.active {
    display: block;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
  }

  .form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #E5E7EB;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
  }

  .form-control:focus {
    outline: none;
    border-color: #C8A8E9;
    box-shadow: 0 0 0 3px rgba(200, 168, 233, 0.1);
  }

  .btn-primary {
    background: linear-gradient(135deg, #6B46C1, #9333EA);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(107, 70, 193, 0.3);
  }

  .btn-success {
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
  }

  .questions-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .question-card {
    background: #F9FAFB;
    border: 2px solid #E5E7EB;
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
  }

  .question-card:hover {
    border-color: #C8A8E9;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }

  .question-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .question-type-badge {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .question-text {
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
    flex: 1;
  }

  .question-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .option {
    padding: 0.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #E5E7EB;
  }

  .option.correct {
    background: #D1FAE5;
    border-color: #10B981;
  }

  .question-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 20px;
  }

  .btn-warning {
    background: #F59E0B;
    color: white;
    border: none;
  }

  .btn-danger {
    background: #EF4444;
    color: white;
    border: none;
  }

  .empty-state {
    text-align: center;
    padding: 3rem;
    color: #6B7280;
  }

  .empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .publish-section {
    background: #F0FDF4;
    border: 2px solid #10B981;
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
  }

  .publish-section h3 {
    color: #059669;
    margin-bottom: 1rem;
  }

  .checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .checkbox-group input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="quiz-edit-container">
  <!-- En-tête du quiz -->
  <div class="quiz-header">
    <h1 class="quiz-title">{{ quiz.title }}</h1>
    <p>{{ quiz.description|default:"Aucune description" }}</p>
    
    <div class="quiz-meta">
      <div class="meta-item">
        <i class="fas fa-book"></i>
        <span>{{ quiz.course.title }}</span>
      </div>
      <div class="meta-item">
        <i class="fas fa-question-circle"></i>
        <span>{{ questions.count }} question{{ questions.count|pluralize }}</span>
      </div>
      <div class="meta-item">
        <i class="fas fa-{{ quiz.correction_type == 'auto' and 'robot' or 'user-edit' }}"></i>
        <span>{{ quiz.get_correction_type_display }}</span>
      </div>
      <div class="meta-item">
        <i class="fas fa-{{ quiz.is_published and 'eye' or 'eye-slash' }}"></i>
        <span>{{ quiz.is_published and 'Publié' or 'Brouillon' }}</span>
      </div>
    </div>
  </div>

  <!-- Onglets -->
  <div class="tabs">
    <button class="tab-button active" onclick="showTab('settings')">
      <i class="fas fa-cog"></i> Paramètres
    </button>
    <button class="tab-button" onclick="showTab('questions')">
      <i class="fas fa-list"></i> Questions ({{ questions.count }})
    </button>
    <button class="tab-button" onclick="showTab('publish')">
      <i class="fas fa-share"></i> Publication
    </button>
  </div>

  <!-- Contenu des onglets -->
  
  <!-- Onglet Paramètres -->
  <div id="settings-tab" class="tab-content active">
    <h2><i class="fas fa-cog"></i> Paramètres du quiz</h2>
    
    <form method="post">
      {% csrf_token %}
      
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="title" class="form-label">Titre du quiz</label>
            <input type="text" id="title" name="title" class="form-control" value="{{ quiz.title }}" required>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="form-group">
            <label for="correction_type" class="form-label">Type de correction</label>
            <select id="correction_type" name="correction_type" class="form-control">
              <option value="auto" {% if quiz.correction_type == 'auto' %}selected{% endif %}>Correction automatique</option>
              <option value="manual" {% if quiz.correction_type == 'manual' %}selected{% endif %}>Correction manuelle</option>
            </select>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="description" class="form-label">Description</label>
        <textarea id="description" name="description" class="form-control" rows="3">{{ quiz.description }}</textarea>
      </div>

      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            <label for="time_limit" class="form-label">Temps limite (minutes)</label>
            <input type="number" id="time_limit" name="time_limit" class="form-control" 
                   value="{{ quiz.time_limit|default:'' }}" min="1" max="300">
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="form-group">
            <label for="max_attempts" class="form-label">Tentatives maximum</label>
            <input type="number" id="max_attempts" name="max_attempts" class="form-control" 
                   value="{{ quiz.max_attempts }}" min="1" max="10" required>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="form-group">
            <label for="passing_score" class="form-label">Score minimum (%)</label>
            <input type="number" id="passing_score" name="passing_score" class="form-control" 
                   value="{{ quiz.passing_score }}" min="0" max="100" required>
          </div>
        </div>
      </div>

      <div class="text-center">
        <button type="submit" class="btn-primary">
          <i class="fas fa-save"></i>
          Sauvegarder les paramètres
        </button>
      </div>
    </form>
  </div>

  <!-- Onglet Questions -->
  <div id="questions-tab" class="tab-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2><i class="fas fa-list"></i> Questions du quiz</h2>
      <a href="{% url 'quizzes:add_question' quiz.id %}" class="btn-success">
        <i class="fas fa-plus"></i>
        Ajouter une question
      </a>
    </div>

    {% if questions %}
      <div class="questions-list">
        {% for question in questions %}
          <div class="question-card">
            <div class="question-header">
              <div class="question-type-badge">
                {{ question.get_question_type_display }}
              </div>
              <div class="question-actions">
                <button class="btn btn-warning btn-sm">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger btn-sm">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
            
            <div class="question-text">
              {{ question.text }}
            </div>

            {% if question.is_multiple_choice %}
              <div class="question-options">
                <div class="option {% if question.correct_option == 'A' %}correct{% endif %}">
                  A) {{ question.option_a }}
                </div>
                <div class="option {% if question.correct_option == 'B' %}correct{% endif %}">
                  B) {{ question.option_b }}
                </div>
                <div class="option {% if question.correct_option == 'C' %}correct{% endif %}">
                  C) {{ question.option_c }}
                </div>
                <div class="option {% if question.correct_option == 'D' %}correct{% endif %}">
                  D) {{ question.option_d }}
                </div>
              </div>
            {% elif question.is_true_false %}
              <div class="question-options">
                <div class="option {% if question.correct_answer_boolean %}correct{% endif %}">
                  <i class="fas fa-check"></i> Vrai
                </div>
                <div class="option {% if not question.correct_answer_boolean %}correct{% endif %}">
                  <i class="fas fa-times"></i> Faux
                </div>
              </div>
            {% elif question.correct_answer_text %}
              <div class="option correct">
                <strong>Réponse attendue:</strong> {{ question.correct_answer_text|truncatewords:10 }}
              </div>
            {% endif %}

            {% if question.explanation %}
              <div class="mt-2">
                <small class="text-muted">
                  <strong>Explication:</strong> {{ question.explanation|truncatewords:15 }}
                </small>
              </div>
            {% endif %}
          </div>
        {% endfor %}
      </div>
    {% else %}
      <div class="empty-state">
        <i class="fas fa-question-circle"></i>
        <h3>Aucune question</h3>
        <p>Commencez par ajouter des questions à votre quiz.</p>
        <a href="{% url 'quizzes:add_question' quiz.id %}" class="btn-success">
          <i class="fas fa-plus"></i>
          Ajouter la première question
        </a>
      </div>
    {% endif %}
  </div>

  <!-- Onglet Publication -->
  <div id="publish-tab" class="tab-content">
    <h2><i class="fas fa-share"></i> Publication du quiz</h2>
    
    <div class="publish-section">
      <h3><i class="fas fa-info-circle"></i> État de publication</h3>
      
      <form method="post">
        {% csrf_token %}
        <div class="checkbox-group">
          <input type="checkbox" id="is_published" name="is_published" 
                 {% if quiz.is_published %}checked{% endif %}>
          <label for="is_published" class="form-label">
            Publier ce quiz (les étudiants pourront le voir et le passer)
          </label>
        </div>
        
        {% if questions.count == 0 %}
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            Vous devez ajouter au moins une question avant de pouvoir publier le quiz.
          </div>
        {% else %}
          <button type="submit" class="btn-primary">
            <i class="fas fa-save"></i>
            {% if quiz.is_published %}
              Mettre à jour la publication
            {% else %}
              Publier le quiz
            {% endif %}
          </button>
        {% endif %}
      </form>
    </div>
  </div>
</div>

<script>
function showTab(tabName) {
  // Masquer tous les contenus d'onglets
  const tabContents = document.querySelectorAll('.tab-content');
  tabContents.forEach(content => content.classList.remove('active'));
  
  // Désactiver tous les boutons d'onglets
  const tabButtons = document.querySelectorAll('.tab-button');
  tabButtons.forEach(button => button.classList.remove('active'));
  
  // Afficher le contenu de l'onglet sélectionné
  document.getElementById(tabName + '-tab').classList.add('active');
  
  // Activer le bouton de l'onglet sélectionné
  event.target.classList.add('active');
}
</script>
{% endblock %}
