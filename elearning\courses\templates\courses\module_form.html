{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %}{{ action }} Module{% endblock %}
{% block content %}
<div class="container py-4">
  <h3>{{ action }} Module for {{ course.title }}</h3>
  <form method="post">
    {% csrf_token %}
    {{ form|crispy }}
    <button type="submit" class="btn btn-success">{{ action }}</button>
    <a href="{% url 'module_list' course.id %}" class="btn btn-secondary">Cancel</a>
  </form>
</div>
{% endblock %}