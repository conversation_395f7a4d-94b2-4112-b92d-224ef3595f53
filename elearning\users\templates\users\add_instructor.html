{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Ajouter un Instructeur | E-Learn+{% endblock %}

{% block extra_css %}
<style>
  /* Container principal moderne */
  .instructor-form-container {
    max-width: 600px;
    margin: 40px auto;
    background: white;
    border-radius: 24px;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
  }

  /* En-tête avec dégradé moderne */
  .instructor-form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .instructor-form-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
  }

  .instructor-form-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  }

  .instructor-form-title i {
    font-size: 2.5rem;
    margin-right: 15px;
    opacity: 0.9;
  }

  .instructor-form-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    position: relative;
    z-index: 2;
  }

  /* Corps du formulaire */
  .form-body {
    padding: 40px;
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    position: relative;
  }

  .form-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23667eea' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E") repeat;
    pointer-events: none;
  }

  /* Groupes de champs */
  .form-group {
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
  }

  /* Labels avec icônes */
  .form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    font-size: 1rem;
  }

  .form-label i {
    margin-right: 10px;
    color: #667eea;
    width: 20px;
    text-align: center;
  }

  /* Champs de saisie modernes */
  .form-control {
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: 16px;
    padding: 18px 24px;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 100%;
  }

  .form-control:hover {
    border-color: rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
  }

  .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), 0 12px 40px rgba(102, 126, 234, 0.2);
    background: white;
    outline: none;
    transform: translateY(-3px);
  }

  .form-control::placeholder {
    color: rgba(102, 126, 234, 0.5);
    font-style: italic;
  }

  /* Container pour les champs de mot de passe */
  .password-input-container {
    position: relative;
  }

  .password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s;
  }

  .password-toggle:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #764ba2;
  }



  /* Texte d'aide */
  .form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 8px;
    font-style: italic;
  }

  /* Container pour les boutons */
  .button-container {
    text-align: center;
    margin-top: 40px;
  }

  /* Bouton de soumission moderne */
  .btn-submit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
  }

  .btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
  }

  .btn-submit:hover::before {
    left: 100%;
  }

  .btn-submit:active {
    transform: translateY(-1px);
  }

  .btn-submit i {
    margin-right: 10px;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .instructor-form-container {
      margin: 20px;
      border-radius: 16px;
    }

    .instructor-form-header {
      padding: 30px 20px;
    }

    .instructor-form-title {
      font-size: 1.8rem;
    }

    .form-body {
      padding: 30px 20px;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="instructor-form-container">
  <div class="instructor-form-header">
    <h1 class="instructor-form-title">
      <i class="fas fa-chalkboard-teacher"></i>
      Ajouter un Instructeur
    </h1>
    <p>Créez un compte pour un nouvel instructeur sur la plateforme</p>
  </div>

  <div class="form-body">
    <form method="post" novalidate autocomplete="off">
      {% csrf_token %}

      <div class="form-group">
        <label for="id_username" class="form-label">
          <i class="fas fa-user"></i>
          Nom d'utilisateur*
        </label>
        <input type="text" name="username" maxlength="150" class="form-control" required id="id_username" autocomplete="new-username" placeholder="Ex: jean.dupont">
      </div>

      <div class="form-group">
        <label for="id_email" class="form-label">
          <i class="fas fa-envelope"></i>
          Email
        </label>
        <input type="email" name="email" class="form-control" id="id_email" autocomplete="new-email" placeholder="<EMAIL>">
      </div>

      <div class="form-group">
        <label for="id_password1" class="form-label">
          <i class="fas fa-lock"></i>
          Mot de passe*
        </label>
        <div class="password-input-container">
          <input type="password" name="password1" class="form-control" required id="id_password1" autocomplete="new-password" placeholder="Minimum 8 caractères">
          <button type="button" class="password-toggle" onclick="togglePassword('id_password1')">
            <i class="fas fa-eye" id="eye-id_password1"></i>
          </button>
        </div>
        <small class="form-text">Minimum 8 caractères, pas trop simple</small>
      </div>

      <div class="form-group">
        <label for="id_password2" class="form-label">
          <i class="fas fa-lock"></i>
          Confirmer le mot de passe*
        </label>
        <div class="password-input-container">
          <input type="password" name="password2" class="form-control" required id="id_password2" autocomplete="new-password" placeholder="Répétez le mot de passe">
          <button type="button" class="password-toggle" onclick="togglePassword('id_password2')">
            <i class="fas fa-eye" id="eye-id_password2"></i>
          </button>
        </div>
      </div>

      <input type="hidden" name="role" value="instructor">

      <div class="button-container">
        <button type="submit" class="btn-submit">
          <i class="fas fa-user-plus"></i>
          Créer l'instructeur
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById('eye-' + fieldId);

    if (field.type === 'password') {
      field.type = 'text';
      eye.classList.remove('fa-eye');
      eye.classList.add('fa-eye-slash');
    } else {
      field.type = 'password';
      eye.classList.remove('fa-eye-slash');
      eye.classList.add('fa-eye');
    }
  }

  // Script pour effacer les champs au chargement de la page
  document.addEventListener('DOMContentLoaded', function() {
    // Réinitialiser les champs du formulaire
    document.getElementById('id_username').value = '';
    document.getElementById('id_email').value = '';
    document.getElementById('id_password1').value = '';
    document.getElementById('id_password2').value = '';
  });
</script>
{% endblock %}
