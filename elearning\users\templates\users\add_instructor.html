{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Ajouter un Instructeur | E-Learn+{% endblock %}

{% block content %}
<div class="instructor-form-container">
  <div class="instructor-form-header">
    <h1 class="instructor-form-title">
      <i class="fas fa-chalkboard-teacher"></i>
      Ajouter un Instructeur
    </h1>
    <p class="text-muted">Créez un compte pour un nouvel instructeur sur la plateforme</p>
  </div>

  <form method="post" novalidate autocomplete="off">
    {% csrf_token %}
    
    <div class="form-group">
      <label for="id_username" class="form-label">Nom d'utilisateur*</label>
      <input type="text" name="username" maxlength="150" class="form-control" required id="id_username" autocomplete="new-username">
      <small class="form-text">Requis. 150 caractères ou moins. Lettres, chiffres et @/./+/-/_ uniquement.</small>
    </div>
    
    <div class="form-group">
      <label for="id_email" class="form-label">Adresse email</label>
      <input type="email" name="email" class="form-control" id="id_email" autocomplete="new-email">
    </div>
    
    <div class="form-group">
      <label for="id_password1" class="form-label">Mot de passe*</label>
      <input type="password" name="password1" class="form-control" required id="id_password1" autocomplete="new-password">
      
      <div class="password-requirements">
        <ul>
          <li>Votre mot de passe ne peut pas être trop similaire à vos autres informations personnelles.</li>
          <li>Votre mot de passe doit contenir au moins 8 caractères.</li>
          <li>Votre mot de passe ne peut pas être un mot de passe couramment utilisé.</li>
          <li>Votre mot de passe ne peut pas être entièrement numérique.</li>
        </ul>
      </div>
    </div>
    
    <div class="form-group">
      <label for="id_password2" class="form-label">Confirmation du mot de passe*</label>
      <input type="password" name="password2" class="form-control" required id="id_password2" autocomplete="new-password">
      <small class="form-text">Entrez le même mot de passe que précédemment, pour vérification.</small>
    </div>
    
    <input type="hidden" name="role" value="instructor">
    
    <button type="submit" class="btn-submit">Créer</button>
  </form>
</div>

<script>
  // Script pour effacer les champs au chargement de la page
  document.addEventListener('DOMContentLoaded', function() {
    // Réinitialiser les champs du formulaire
    document.getElementById('id_username').value = '';
    document.getElementById('id_email').value = '';
    document.getElementById('id_password1').value = '';
    document.getElementById('id_password2').value = '';
  });
</script>
{% endblock %}
