from django.shortcuts import render, get_object_or_404, redirect
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db.models import Q
from django.contrib.auth.decorators import login_required
from .models import Course, Module, Lesson

def course_list(request):
    # Récupérer tous les cours
    courses_list = Course.objects.all()
    
    # Filtrage par recherche
    search_query = request.GET.get('search', '')
    if search_query:
        courses_list = courses_list.filter(
            Q(title__icontains=search_query) | 
            Q(description__icontains=search_query)
        )
    
    # Filtrage par catégorie
    category = request.GET.get('category', '')
    if category:
        # Utiliser category__name au lieu de category__iexact
        courses_list = courses_list.filter(category__name__iexact=category)
    
    # Pagination
    paginator = Paginator(courses_list, 9)  # 9 cours par page
    page = request.GET.get('page')
    courses = paginator.get_page(page)
    
    context = {
        'courses': courses,
    }
    
    return render(request, 'courses/course_list.html', context)

def course_detail(request, course_id):
    course = get_object_or_404(Course, id=course_id)
    
    context = {
        'course': course,
    }
    
    return render(request, 'courses/course_detail.html', context)

def course_detail_by_slug(request, slug):
    """Vue pour afficher les détails d'un cours par son slug"""
    course = get_object_or_404(Course, slug=slug)
    
    context = {
        'course': course,
    }
    
    return render(request, 'courses/course_detail.html', context)

@login_required
def module_list(request, course_id):
    """Vue pour afficher la liste des modules d'un cours"""
    course = get_object_or_404(Course, id=course_id)
    modules = Module.objects.filter(course=course).order_by('order')
    
    context = {
        'course': course,
        'modules': modules,
    }
    
    return render(request, 'courses/module_list.html', context)

@login_required
def module_detail(request, course_id, module_id):
    """Vue pour afficher les détails d'un module"""
    course = get_object_or_404(Course, id=course_id)
    module = get_object_or_404(Module, id=module_id, course=course)
    lessons = Lesson.objects.filter(module=module).order_by('order')
    
    context = {
        'course': course,
        'module': module,
        'lessons': lessons,
    }
    
    return render(request, 'courses/module_detail.html', context)

@login_required
def lesson_detail(request, course_id, module_id, lesson_id):
    """Vue pour afficher les détails d'une leçon"""
    course = get_object_or_404(Course, id=course_id)
    module = get_object_or_404(Module, id=module_id, course=course)
    lesson = get_object_or_404(Lesson, id=lesson_id, module=module)
    
    context = {
        'course': course,
        'module': module,
        'lesson': lesson,
    }
    
    return render(request, 'courses/lesson_detail.html', context)

@login_required
def enroll_course(request, course_id):
    """Vue pour s'inscrire à un cours"""
    course = get_object_or_404(Course, id=course_id)
    
    # Vérifier si l'utilisateur est déjà inscrit
    if request.user in course.students.all():
        # Déjà inscrit, rediriger vers la page du cours
        return redirect('course_detail', course_id=course.id)
    
    # Inscrire l'utilisateur au cours
    course.students.add(request.user)
    course.save()
    
    return redirect('course_detail', course_id=course.id)

@login_required
def create_course(request):
    """Vue pour créer un nouveau cours"""
    # Logique pour créer un cours
    return render(request, 'courses/create_course.html')

@login_required
def update_course(request, course_id):
    """Vue pour mettre à jour un cours"""
    course = get_object_or_404(Course, id=course_id)
    # Vérifier que l'utilisateur est l'instructeur du cours
    
    return render(request, 'courses/update_course.html', {'course': course})

@login_required
def delete_course(request, course_id):
    """Vue pour supprimer un cours"""
    course = get_object_or_404(Course, id=course_id)
    # Vérifier que l'utilisateur est l'instructeur du cours
    
    if request.method == 'POST':
        course.delete()
        return redirect('course_list')
    
    return render(request, 'courses/delete_course.html', {'course': course})

def course_catalog(request):
    """Vue pour afficher le catalogue des cours."""
    category = request.GET.get('category')
    
    if category:
        courses = Course.objects.filter(category=category, is_published=True).order_by('-created_at')
    else:
        courses = Course.objects.filter(is_published=True).order_by('-created_at')
    
    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(courses, 9)  # 9 cours par page
    
    try:
        courses = paginator.page(page)
    except PageNotAnInteger:
        courses = paginator.page(1)
    except EmptyPage:
        courses = paginator.page(paginator.num_pages)
    
    context = {
        'courses': courses,
        'category': category,
    }
    
    return render(request, 'courses/course_catalog.html', context)

def search_courses(request):
    """Vue pour rechercher des cours."""
    query = request.GET.get('q', '')
    
    if query:
        courses = Course.objects.filter(
            Q(title__icontains=query) | 
            Q(description__icontains=query),
            is_published=True
        ).order_by('-created_at')
    else:
        courses = Course.objects.filter(is_published=True).order_by('-created_at')
    
    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(courses, 9)  # 9 cours par page
    
    try:
        courses = paginator.page(page)
    except PageNotAnInteger:
        courses = paginator.page(1)
    except EmptyPage:
        courses = paginator.page(paginator.num_pages)
    
    context = {
        'courses': courses,
        'query': query,
    }
    
    return render(request, 'courses/search_results.html', context)
