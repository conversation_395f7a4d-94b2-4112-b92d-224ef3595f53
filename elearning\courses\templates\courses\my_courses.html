{% extends 'base.html' %}

{% block title %}Mes Cours - E-Learn+{% endblock %}

{% block content %}
<div class="container py-5">
  <h1 class="mb-4" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700;">Mes Cours</h1>
  
  <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
    <li class="nav-item" role="presentation">
      <button class="nav-link active" id="enrolled-tab" data-bs-toggle="tab" data-bs-target="#enrolled" type="button" role="tab" aria-controls="enrolled" aria-selected="true">Cours suivis</button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab" aria-controls="completed" aria-selected="false">Cours terminés</button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="wishlist-tab" data-bs-toggle="tab" data-bs-target="#wishlist" type="button" role="tab" aria-controls="wishlist" aria-selected="false">Liste de souhaits</button>
    </li>
  </ul>
  
  <div class="tab-content" id="myTabContent">
    <!-- Cours suivis -->
    <div class="tab-pane fade show active" id="enrolled" role="tabpanel" aria-labelledby="enrolled-tab">
      <div class="row g-4">
        <!-- Exemple de cours 1 -->
        <div class="col-md-6 col-lg-4">
          <div class="card h-100">
            <img src="https://via.placeholder.com/300x150" class="card-img-top" alt="Cours de Python">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="badge bg-primary">Programmation</span>
                <small class="text-muted">Progression: 65%</small>
              </div>
              <h5 class="card-title">Introduction à Python</h5>
              <p class="card-text text-muted">Apprenez les bases de la programmation avec Python, un langage puissant et facile à apprendre.</p>
              <div class="progress mb-3" style="height: 8px;">
                <div class="progress-bar bg-success" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
            <div class="card-footer bg-transparent border-0">
              <a href="#" class="btn btn-primary w-100">Continuer</a>
            </div>
          </div>
        </div>
        
        <!-- Exemple de cours 2 -->
        <div class="col-md-6 col-lg-4">
          <div class="card h-100">
            <img src="https://via.placeholder.com/300x150" class="card-img-top" alt="Cours de JavaScript">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="badge bg-primary">Développement Web</span>
                <small class="text-muted">Progression: 30%</small>
              </div>
              <h5 class="card-title">JavaScript Moderne</h5>
              <p class="card-text text-muted">Maîtrisez JavaScript ES6+ et les concepts avancés pour créer des applications web interactives.</p>
              <div class="progress mb-3" style="height: 8px;">
                <div class="progress-bar bg-success" role="progressbar" style="width: 30%;" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
            <div class="card-footer bg-transparent border-0">
              <a href="#" class="btn btn-primary w-100">Continuer</a>
            </div>
          </div>
        </div>
        
        <!-- Exemple de cours 3 -->
        <div class="col-md-6 col-lg-4">
          <div class="card h-100">
            <img src="https://via.placeholder.com/300x150" class="card-img-top" alt="Cours de Data Science">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="badge bg-primary">Data Science</span>
                <small class="text-muted">Progression: 10%</small>
              </div>
              <h5 class="card-title">Introduction à la Data Science</h5>
              <p class="card-text text-muted">Découvrez les fondamentaux de la data science et de l'analyse de données avec Python.</p>
              <div class="progress mb-3" style="height: 8px;">
                <div class="progress-bar bg-success" role="progressbar" style="width: 10%;" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
            <div class="card-footer bg-transparent border-0">
              <a href="#" class="btn btn-primary w-100">Continuer</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Cours terminés -->
    <div class="tab-pane fade" id="completed" role="tabpanel" aria-labelledby="completed-tab">
      <div class="row g-4">
        <!-- Exemple de cours terminé 1 -->
        <div class="col-md-6 col-lg-4">
          <div class="card h-100">
            <img src="https://via.placeholder.com/300x150" class="card-img-top" alt="Cours HTML/CSS">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="badge bg-success">Terminé</span>
                <small class="text-muted">Note: 95%</small>
              </div>
              <h5 class="card-title">HTML & CSS Fondamentaux</h5>
              <p class="card-text text-muted">Les bases essentielles pour créer et styliser des sites web modernes.</p>
            </div>
            <div class="card-footer bg-transparent border-0 d-flex justify-content-between">
              <a href="#" class="btn btn-outline-primary">Revoir</a>
              <a href="#" class="btn btn-success">Certificat</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Liste de souhaits -->
    <div class="tab-pane fade" id="wishlist" role="tabpanel" aria-labelledby="wishlist-tab">
      <div class="row g-4">
        <!-- Exemple de cours souhaité 1 -->
        <div class="col-md-6 col-lg-4">
          <div class="card h-100">
            <img src="https://via.placeholder.com/300x150" class="card-img-top" alt="Cours React">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="badge bg-secondary">Framework</span>
                <small class="text-muted">4.8 ★ (420 avis)</small>
              </div>
              <h5 class="card-title">React.js - De Débutant à Expert</h5>
              <p class="card-text text-muted">Apprenez à créer des applications web modernes avec React, Redux et les hooks.</p>
              <p class="fw-bold text-primary">59,99 €</p>
            </div>
            <div class="card-footer bg-transparent border-0 d-flex justify-content-between">
              <button class="btn btn-outline-danger"><i class="fas fa-trash-alt"></i></button>
              <a href="#" class="btn btn-primary">S'inscrire</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
