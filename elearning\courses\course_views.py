from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib import messages
from .models import Course
from .forms import CourseForm

def course_list(request):
    """Vue pour afficher la liste des cours avec filtrage et recherche."""
    # Récupérer tous les cours
    courses_list = Course.objects.all()
    
    # Filtrage par recherche
    search_query = request.GET.get('search', '')
    if search_query:
        courses_list = courses_list.filter(
            Q(title__icontains=search_query) | 
            Q(description__icontains=search_query)
        )
    
    # Filtrage par catégorie
    category = request.GET.get('category', '')
    if category:
        courses_list = courses_list.filter(category=category)
    
    # Filtrage par niveau
    level = request.GET.get('level', '')
    if level:
        courses_list = courses_list.filter(level=level)
    
    # Pagination
    paginator = Paginator(courses_list, 9)  # 9 cours par page
    page = request.GET.get('page')
    courses = paginator.get_page(page)
    
    context = {
        'courses': courses,
        'search_query': search_query,
        'selected_category': category,
        'selected_level': level
    }
    
    return render(request, 'courses/course_list.html', context)

@login_required
def add_course(request):
    """Vue pour ajouter un nouveau cours."""
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES)
        if form.is_valid():
            course = form.save(commit=False)
            # Si l'instructeur n'est pas défini, utiliser l'utilisateur actuel
            if not course.instructor:
                course.instructor = request.user
            course.save()
            messages.success(request, "Le cours a été créé avec succès.")
            return redirect('course_list')
    else:
        form = CourseForm(initial={'instructor': request.user})
    
    return render(request, 'courses/course_form.html', {
        'form': form, 
        'action': 'Ajouter'
    })

@login_required
def edit_course(request, course_id):
    """Vue pour modifier un cours existant."""
    course = get_object_or_404(Course, id=course_id)
    
    # Vérifier que l'utilisateur est l'instructeur du cours ou un administrateur
    if course.instructor != request.user and not request.user.is_staff:
        messages.error(request, "Vous n'avez pas la permission de modifier ce cours.")
        return redirect('course_list')
    
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES, instance=course)
        if form.is_valid():
            form.save()
            messages.success(request, "Le cours a été mis à jour avec succès.")
            return redirect('course_list')
    else:
        form = CourseForm(instance=course)
    
    return render(request, 'courses/course_form.html', {
        'form': form, 
        'action': 'Modifier'
    })

@login_required
def delete_course(request, course_id):
    """Vue pour supprimer un cours."""
    course = get_object_or_404(Course, id=course_id)
    
    # Vérifier que l'utilisateur est l'instructeur du cours ou un administrateur
    if course.instructor != request.user and not request.user.is_staff:
        messages.error(request, "Vous n'avez pas la permission de supprimer ce cours.")
        return redirect('course_list')
    
    if request.method == 'POST':
        course.delete()
        messages.success(request, "Le cours a été supprimé avec succès.")
        return redirect('course_list')
    
    return render(request, 'courses/course_confirm_delete.html', {
        'course': course
    })

def course_detail(request, course_id):
    """Vue pour afficher les détails d'un cours."""
    course = get_object_or_404(Course, id=course_id)
    
    # Vérifier si l'utilisateur est inscrit au cours
    is_enrolled = False
    if request.user.is_authenticated:
        is_enrolled = course.students.filter(id=request.user.id).exists()
    
    context = {
        'course': course,
        'is_enrolled': is_enrolled
    }
    
    return render(request, 'courses/course_detail.html', context)

@login_required
def enroll_course(request, course_id):
    """Vue pour s'inscrire à un cours"""
    course = get_object_or_404(Course, id=course_id)
    
    # Vérifier si l'utilisateur est déjà inscrit
    if request.user in course.students.all():
        # Déjà inscrit, rediriger vers la page du cours
        return redirect('course_detail_by_id', course_id=course.id)
    
    # Inscrire l'utilisateur au cours
    course.students.add(request.user)
    course.save()
    
    return redirect('course_detail_by_id', course_id=course.id)




