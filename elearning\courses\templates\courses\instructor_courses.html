{% extends 'base.html' %}
{% load static %}

{% block title %}Mes Cours - Instructeur{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .courses-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .courses-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    display: flex;
    justify-content: between;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .create-btn {
    background: white;
    color: #333;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .create-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #333;
    text-decoration: none;
  }

  .courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
  }

  .course-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
  }

  .course-card:hover {
    transform: translateY(-10px);
  }

  .course-image {
    height: 200px;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
  }

  .course-content {
    padding: 1.5rem;
  }

  .course-title {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #333;
  }

  .course-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
  }

  .course-stats {
    display: flex;
    justify-content: between;
    margin-bottom: 1rem;
    font-size: 0.8rem;
    color: #666;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .course-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-bottom: 1rem;
  }

  .status-published {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
  }

  .status-draft {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
    color: white;
  }

  .course-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }

  .btn-success {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
  }

  .btn-warning {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
    color: white;
  }

  .btn-danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #C8A8E9;
  }
</style>
{% endblock %}

{% block content %}
<div class="courses-container">
  <div class="courses-header">
    <div>
      <h1><i class="fas fa-book"></i> Mes Cours</h1>
      <p>Gérez et suivez tous vos cours</p>
    </div>
    <a href="{% url 'instructor_create_course' %}" class="create-btn">
      <i class="fas fa-plus"></i>
      Créer un Nouveau Cours
    </a>
  </div>

  {% if courses %}
  <div class="courses-grid">
    {% for course in courses %}
    <div class="course-card">
      <div class="course-image">
        {% if course.image %}
          <img src="{{ course.image.url }}" alt="{{ course.title }}" style="width: 100%; height: 100%; object-fit: cover;">
        {% else %}
          <i class="fas fa-graduation-cap"></i>
        {% endif %}
      </div>
      
      <div class="course-content">
        <h3 class="course-title">{{ course.title }}</h3>
        <p class="course-description">{{ course.description|truncatewords:20 }}</p>
        
        <div class="course-stats">
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span>{{ course.student_count }} étudiants</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-play-circle"></i>
            <span>{{ course.lesson_count }} leçons</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-tag"></i>
            <span>{{ course.get_category_display }}</span>
          </div>
        </div>
        
        <div class="course-status {% if course.is_published %}status-published{% else %}status-draft{% endif %}">
          {% if course.is_published %}
            <i class="fas fa-check-circle"></i> Publié
          {% else %}
            <i class="fas fa-clock"></i> Brouillon
          {% endif %}
        </div>
        
        <div class="course-actions">
          <a href="{% url 'instructor_course_detail' course.id %}" class="action-btn btn-primary">
            <i class="fas fa-eye"></i> Voir
          </a>
          <a href="{% url 'instructor_edit_course' course.id %}" class="action-btn btn-warning">
            <i class="fas fa-edit"></i> Modifier
          </a>
          <a href="{% url 'instructor_course_students' course.id %}" class="action-btn btn-success">
            <i class="fas fa-users"></i> Étudiants
          </a>
          <a href="{% url 'instructor_delete_course' course.id %}" class="action-btn btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce cours ?')">
            <i class="fas fa-trash"></i> Supprimer
          </a>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
  {% else %}
  <div class="empty-state">
    <div class="empty-icon">
      <i class="fas fa-book-open"></i>
    </div>
    <h3>Aucun cours créé</h3>
    <p>Commencez par créer votre premier cours pour vos étudiants</p>
    <a href="{% url 'instructor_create_course' %}" class="create-btn">
      <i class="fas fa-plus"></i>
      Créer mon Premier Cours
    </a>
  </div>
  {% endif %}
</div>
{% endblock %}
