from django.contrib import admin
from .models import Course, Module, Lesson

class LessonInline(admin.TabularInline):
    model = Lesson
    extra = 1

class ModuleInline(admin.TabularInline):
    model = Module
    extra = 1

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ['title', 'instructor', 'category', 'is_published', 'created_at']
    list_filter = ['category', 'is_published', 'level']
    search_fields = ['title', 'description']
    date_hierarchy = 'created_at'
    inlines = [ModuleInline]
    
    fieldsets = (
        (None, {
            'fields': ('title', 'instructor', 'description')
        }),
        ('Détails du cours', {
            'fields': ('category', 'level')
        }),
        ('Publication', {
            'fields': ('is_published',)
        }),
    )

@admin.register(Module)
class ModuleAdmin(admin.ModelAdmin):
    list_display = ['title', 'course', 'order']
    list_filter = ['course']
    search_fields = ['title']
    inlines = [LessonInline]

@admin.register(Lesson)
class LessonAdmin(admin.ModelAdmin):
    list_display = ['title', 'module', 'order']
    list_filter = ['module']
    search_fields = ['title', 'content']
