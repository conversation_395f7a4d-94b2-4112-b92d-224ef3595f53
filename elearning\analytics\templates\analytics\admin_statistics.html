{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
  .stats-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .stats-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-5px);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
  }

  .stat-icon.users { background: linear-gradient(135deg, #667eea, #764ba2); }
  .stat-icon.courses { background: linear-gradient(135deg, #f093fb, #f5576c); }
  .stat-icon.lessons { background: linear-gradient(135deg, #4facfe, #00f2fe); }
  .stat-icon.enrollments { background: linear-gradient(135deg, #43e97b, #38f9d7); }

  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: #666;
    font-size: 0.9rem;
  }

  .chart-container {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .export-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .export-btn {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
  }

  .export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #333;
    text-decoration: none;
  }

  .popular-courses, .active-instructors {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .list-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
  }

  .list-item:last-child {
    border-bottom: none;
  }

  .badge-count {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
  }
</style>
{% endblock %}

{% block content %}
<div class="stats-container">
  <div class="stats-header">
    <h1><i class="fas fa-chart-bar"></i> Tableau de Bord - Statistiques</h1>
    <p>Vue d'ensemble de la plateforme E-Learn+</p>
  </div>

  <!-- Statistiques principales -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon users">
        <i class="fas fa-users"></i>
      </div>
      <div class="stat-number">{{ total_users }}</div>
      <div class="stat-label">Utilisateurs Total</div>
      <small class="text-muted">{{ total_students }} étudiants, {{ total_instructors }} instructeurs</small>
    </div>

    <div class="stat-card">
      <div class="stat-icon courses">
        <i class="fas fa-book"></i>
      </div>
      <div class="stat-number">{{ total_courses }}</div>
      <div class="stat-label">Cours Total</div>
      <small class="text-muted">{{ published_courses }} publiés</small>
    </div>

    <div class="stat-card">
      <div class="stat-icon lessons">
        <i class="fas fa-play-circle"></i>
      </div>
      <div class="stat-number">{{ total_lessons }}</div>
      <div class="stat-label">Leçons Total</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon enrollments">
        <i class="fas fa-user-graduate"></i>
      </div>
      <div class="stat-number">{{ total_enrollments }}</div>
      <div class="stat-label">Inscriptions Total</div>
    </div>
  </div>

  <!-- Graphique d'évolution -->
  <div class="chart-container">
    <h3><i class="fas fa-chart-line"></i> Évolution des Inscriptions</h3>
    <canvas id="enrollmentChart" width="400" height="100"></canvas>
  </div>

  <div class="row">
    <!-- Cours populaires -->
    <div class="col-md-6">
      <div class="popular-courses">
        <h3><i class="fas fa-fire"></i> Cours les Plus Populaires</h3>
        {% for course in popular_courses %}
        <div class="list-item">
          <div>
            <strong>{{ course.title }}</strong><br>
            <small class="text-muted">par {{ course.instructor.username }}</small>
          </div>
          <span class="badge-count">{{ course.enrollment_count }} étudiants</span>
        </div>
        {% empty %}
        <p class="text-muted">Aucun cours disponible</p>
        {% endfor %}
      </div>
    </div>

    <!-- Instructeurs actifs -->
    <div class="col-md-6">
      <div class="active-instructors">
        <h3><i class="fas fa-chalkboard-teacher"></i> Instructeurs les Plus Actifs</h3>
        {% for instructor in active_instructors %}
        <div class="list-item">
          <div>
            <strong>{{ instructor.username }}</strong><br>
            <small class="text-muted">{{ instructor.email }}</small>
          </div>
          <span class="badge-count">{{ instructor.course_count }} cours</span>
        </div>
        {% empty %}
        <p class="text-muted">Aucun instructeur disponible</p>
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Section d'export -->
  <div class="export-section">
    <h3><i class="fas fa-download"></i> Exporter les Données</h3>
    <p class="text-muted mb-3">Téléchargez les données de la plateforme au format CSV</p>
    
    <a href="{% url 'export_users_csv' %}" class="export-btn">
      <i class="fas fa-users"></i>
      Exporter les Utilisateurs
    </a>
    
    <a href="{% url 'export_courses_csv' %}" class="export-btn">
      <i class="fas fa-book"></i>
      Exporter les Cours
    </a>
  </div>
</div>

<script>
// Graphique d'évolution des inscriptions
const ctx = document.getElementById('enrollmentChart').getContext('2d');
const monthsData = {{ months_data|safe }};

new Chart(ctx, {
    type: 'line',
    data: {
        labels: monthsData.map(item => item.month),
        datasets: [{
            label: 'Nouvelles inscriptions',
            data: monthsData.map(item => item.users),
            borderColor: 'rgb(200, 168, 233)',
            backgroundColor: 'rgba(200, 168, 233, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: true
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
