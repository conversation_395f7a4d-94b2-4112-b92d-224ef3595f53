#!/usr/bin/env python
"""
Script pour créer des quiz de test
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

from users.models import User
from courses.models import Course
from quizzes.models import Quiz, Question

def main():
    print("🎯 CRÉATION DE QUIZ DE TEST")
    print("=" * 40)
    
    try:
        # R<PERSON>cupérer l'instructeur de test
        instructor = User.objects.get(username='prof_test')
        print(f"✅ Instructeur trouvé: {instructor.username}")
        
        # Récupérer les cours de l'instructeur
        courses = Course.objects.filter(instructor=instructor)
        if not courses.exists():
            print("❌ Aucun cours trouvé pour cet instructeur")
            return
        
        # Créer des quiz de test
        quiz_data = [
            {
                'title': 'Quiz Python - Les Bases',
                'description': 'Testez vos connaissances sur les bases de Python',
                'correction_type': 'auto',
                'time_limit': 30,
                'max_attempts': 3,
                'passing_score': 70,
                'is_published': True
            },
            {
                'title': 'Évaluation Django - Modèles',
                'description': 'Quiz sur les modèles Django et les bases de données',
                'correction_type': 'manual',
                'time_limit': 45,
                'max_attempts': 2,
                'passing_score': 60,
                'is_published': False
            },
            {
                'title': 'Test JavaScript - Fonctions',
                'description': 'Évaluation des connaissances sur les fonctions JavaScript',
                'correction_type': 'auto',
                'time_limit': None,
                'max_attempts': 1,
                'passing_score': 80,
                'is_published': True
            }
        ]
        
        for i, quiz_info in enumerate(quiz_data):
            course = courses[i % courses.count()]  # Répartir les quiz sur les cours disponibles
            
            quiz, created = Quiz.objects.get_or_create(
                title=quiz_info['title'],
                course=course,
                defaults={
                    **quiz_info,
                    'created_by': instructor
                }
            )
            
            if created:
                print(f"✅ Quiz créé: {quiz.title}")
                
                # Ajouter quelques questions de test
                create_sample_questions(quiz)
            else:
                print(f"ℹ️ Quiz existe déjà: {quiz.title}")
        
        print(f"\n🎉 QUIZ CRÉÉS AVEC SUCCÈS!")
        print(f"Total des quiz: {Quiz.objects.filter(created_by=instructor).count()}")
        
    except User.DoesNotExist:
        print("❌ Instructeur 'prof_test' non trouvé")
        print("Créez d'abord un instructeur avec le script apply_all_migrations.py")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def create_sample_questions(quiz):
    """Créer des questions d'exemple pour un quiz"""
    questions_data = [
        {
            'text': 'Quel est le type de données utilisé pour stocker du texte en Python ?',
            'question_type': 'multiple_choice',
            'option_a': 'str',
            'option_b': 'int',
            'option_c': 'float',
            'option_d': 'bool',
            'correct_option': 'A',
            'points': 1,
            'order': 1
        },
        {
            'text': 'Python est un langage de programmation orienté objet.',
            'question_type': 'true_false',
            'correct_answer_boolean': True,
            'points': 1,
            'order': 2
        },
        {
            'text': 'Quelle méthode permet de convertir une chaîne en majuscules ?',
            'question_type': 'short_answer',
            'correct_answer_text': 'upper()',
            'points': 2,
            'order': 3
        }
    ]

    for question_data in questions_data:
        Question.objects.get_or_create(
            quiz=quiz,
            text=question_data['text'],
            defaults=question_data
        )

if __name__ == '__main__':
    main()
