#!/usr/bin/env python
"""
Script de test pour vérifier les URLs de l'espace instructeur
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

from django.urls import reverse
from django.test import Client
from django.contrib.auth.models import User
from users.models import UserProfile
from courses.models import Course, Module, Lesson

def test_instructor_urls():
    """Test des URLs de l'espace instructeur"""
    
    print("=== Test des URLs de l'espace instructeur ===\n")
    
    # URLs à tester
    urls_to_test = [
        ('instructor_dashboard', [], 'Dashboard Instructeur'),
        ('instructor_my_courses', [], 'Mes Cours'),
        ('instructor_create_course', [], 'Créer un Cours'),
    ]
    
    # URLs avec paramètres (nécessitent des objets existants)
    parametric_urls = [
        ('instructor_course_detail', [1], 'Détail du Cours'),
        ('instructor_edit_course', [1], 'Modifier le Cours'),
        ('instructor_delete_course', [1], 'Supprimer le Cours'),
        ('instructor_create_module', [1], 'Créer un Module'),
        ('instructor_edit_module', [1, 1], 'Modifier le Module'),
        ('instructor_delete_module', [1, 1], 'Supprimer le Module'),
        ('instructor_create_lesson', [1, 1], 'Créer une Leçon'),
        ('instructor_edit_lesson', [1, 1, 1], 'Modifier la Leçon'),
        ('instructor_delete_lesson', [1, 1, 1], 'Supprimer la Leçon'),
    ]
    
    # Test des URLs simples
    print("1. URLs simples:")
    for url_name, args, description in urls_to_test:
        try:
            url = reverse(url_name, args=args)
            print(f"   ✓ {description}: {url}")
        except Exception as e:
            print(f"   ✗ {description}: ERREUR - {e}")
    
    print("\n2. URLs avec paramètres:")
    for url_name, args, description in parametric_urls:
        try:
            url = reverse(url_name, args=args)
            print(f"   ✓ {description}: {url}")
        except Exception as e:
            print(f"   ✗ {description}: ERREUR - {e}")
    
    print("\n=== Résumé des fonctionnalités implémentées ===")
    print("✓ Dashboard instructeur avec statistiques")
    print("✓ Gestion complète des cours (CRUD)")
    print("✓ Gestion complète des modules (CRUD)")
    print("✓ Gestion complète des leçons (CRUD)")
    print("✓ Interface utilisateur moderne et responsive")
    print("✓ Sécurité avec décorateurs d'authentification")
    print("✓ Templates avec design cohérent (baby violet theme)")
    
    print("\n=== Instructions pour tester ===")
    print("1. Créer un utilisateur avec le rôle 'instructor'")
    print("2. Se connecter sur http://127.0.0.1:8000/users/login/")
    print("3. Accéder au dashboard: http://127.0.0.1:8000/instructor-dashboard/")
    print("4. Créer un cours, puis des modules et des leçons")
    print("5. Tester toutes les fonctionnalités CRUD")

if __name__ == '__main__':
    test_instructor_urls()
