{% extends 'base.html' %}
{% load static %}

{% block title %}<PERSON><PERSON><PERSON>ionner un cours - <PERSON><PERSON>er un quiz{% endblock %}

{% block extra_css %}
<style>
  .course-selection-container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 2rem;
  }

  .selection-header {
    background: linear-gradient(135deg, #C8A8E9 0%, #A8D0F0 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .selection-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .selection-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6B7280, #4B5563);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(107, 114, 128, 0.3);
    color: white;
  }

  .courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .course-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #E5E7EB;
    position: relative;
  }

  .course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  }

  .course-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .course-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.5rem;
    flex: 1;
  }

  .course-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .status-published {
    background: #D1FAE5;
    color: #065F46;
  }

  .status-draft {
    background: #FEF3C7;
    color: #92400E;
  }

  .course-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6B7280;
    font-size: 0.875rem;
  }

  .course-description {
    color: #6B7280;
    margin-bottom: 1.5rem;
    line-height: 1.5;
  }

  .quiz-count {
    background: #F3F4F6;
    border-radius: 10px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    text-align: center;
  }

  .quiz-count-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #6B46C1;
  }

  .quiz-count-label {
    font-size: 0.875rem;
    color: #6B7280;
  }

  .create-quiz-btn {
    background: linear-gradient(135deg, #6B46C1, #9333EA);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    justify-content: center;
  }

  .create-quiz-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(107, 70, 193, 0.3);
    color: white;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6B7280;
  }

  .empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #374151;
  }

  .empty-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #6B46C1, #9333EA);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(107, 70, 193, 0.3);
    color: white;
  }

  @media (max-width: 768px) {
    .course-selection-container {
      padding: 1rem;
    }
    
    .courses-grid {
      grid-template-columns: 1fr;
    }
    
    .action-buttons {
      flex-direction: column;
      align-items: center;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="course-selection-container">
  <div class="selection-header">
    <h1 class="selection-title">
      <i class="fas fa-plus-circle"></i> Créer un Quiz
    </h1>
    <p class="selection-subtitle">Sélectionnez le cours pour lequel vous souhaitez créer un quiz</p>
  </div>

  <div class="action-buttons">
    <a href="{% url 'quizzes:instructor_quiz_list' %}" class="btn-secondary">
      <i class="fas fa-arrow-left"></i>
      Retour aux Quiz
    </a>
  </div>

  {% if courses %}
    <div class="courses-grid">
      {% for course in courses %}
      <div class="course-card">
        <div class="course-card-header">
          <div>
            <h3 class="course-title">{{ course.title }}</h3>
            <span class="course-status status-{{ course.is_published|yesno:'published,draft' }}">
              {{ course.is_published|yesno:'Publié,Brouillon' }}
            </span>
          </div>
        </div>

        <div class="course-meta">
          <div class="meta-item">
            <i class="fas fa-layer-group"></i>
            <span>{{ course.modules.count }} modules</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-users"></i>
            <span>{{ course.students.count }} étudiants</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar"></i>
            <span>{{ course.created_at|date:"d/m/Y" }}</span>
          </div>
        </div>

        {% if course.description %}
        <p class="course-description">{{ course.description|truncatewords:15 }}</p>
        {% endif %}

        <div class="quiz-count">
          <div class="quiz-count-number">{{ course.quizzes.count }}</div>
          <div class="quiz-count-label">Quiz existants</div>
        </div>

        <a href="{% url 'quizzes:create_quiz' course.id %}" class="create-quiz-btn">
          <i class="fas fa-plus"></i>
          Créer un quiz pour ce cours
        </a>
      </div>
      {% endfor %}
    </div>
  {% else %}
    <div class="empty-state">
      <i class="fas fa-book"></i>
      <h3>Aucun cours disponible</h3>
      <p>Vous devez d'abord créer un cours avant de pouvoir créer des quiz</p>
      <a href="{% url 'instructor_create_course' %}" class="btn-primary">
        <i class="fas fa-plus"></i>
        Créer un cours
      </a>
    </div>
  {% endif %}
</div>
{% endblock %}
