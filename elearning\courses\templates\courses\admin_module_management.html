{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Gestion des Modules | {{ course.title }} | E-Learn+{% endblock %}

{% block content %}
<div class="container py-4">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
      <li class="breadcrumb-item"><a href="{% url 'admin_courses' %}">Cours</a></li>
      <li class="breadcrumb-item active">Modules de {{ course.title }}</li>
    </ol>
  </nav>

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Modules du cours: {{ course.title }}</h2>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModuleModal">
      <i class="fas fa-plus me-1"></i> Ajouter un module
    </button>
  </div>

  <!-- Liste des modules -->
  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <div class="list-group">
        {% for module in modules %}
        <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
          <div>
            <h5 class="mb-1">{{ module.title }}</h5>
            <small class="text-muted">{{ module.lesson_set.count }} leçons</small>
          </div>
          <div class="btn-group">
            <a href="{% url 'update_module' module.id %}" class="btn btn-sm btn-outline-primary">
              <i class="fas fa-edit"></i>
            </a>
            <a href="{% url 'manage_lessons' module.id %}" class="btn btn-sm btn-outline-secondary">
              <i class="fas fa-list"></i> Leçons
            </a>
            <a href="{% url 'delete_module' module.id %}" class="btn btn-sm btn-outline-danger">
              <i class="fas fa-trash"></i>
            </a>
          </div>
        </div>
        {% empty %}
        <div class="text-center py-4">
          <p class="text-muted mb-3">Aucun module trouvé pour ce cours</p>
          <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModuleModal">
            <i class="fas fa-plus me-1"></i> Ajouter un module
          </button>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Modal pour ajouter un module -->
  <div class="modal fade" id="addModuleModal" tabindex="-1" aria-labelledby="addModuleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addModuleModalLabel">Ajouter un module</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form method="post" action="{% url 'create_module' course.id %}">
          <div class="modal-body">
            {% csrf_token %}
            {{ module_form|crispy }}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="submit" class="btn btn-primary">Ajouter</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}