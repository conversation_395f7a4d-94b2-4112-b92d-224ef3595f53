{% extends 'base.html' %}

{% block title %}{{ action }} un cours{% endblock %}

{% block content %}
<div class="container py-4">
  <div class="card shadow-sm">
    <div class="card-header bg-primary text-white">
      <h5 class="mb-0">{{ action }} un cours</h5>
    </div>
    <div class="card-body">
      <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        <style>
          /* Masquer le titre dupliqué */
          .blue-header, .duplicate-title {
            display: none !important;
          }
          
          /* Style pour le titre principal */
          .main-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
          
          /* Ajouter des icônes aux champs */
          .field-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6930c3;
            opacity: 0.5;
          }
          
          .field-container {
            position: relative;
          }
          
          /* Effet de survol sur les champs */
          .field-container:hover .field-icon {
            opacity: 1;
            transform: translateY(-50%) scale(1.2);
            transition: all 0.3s;
          }
          
          /* Ajouter un fond subtil */
          body {
            background: linear-gradient(135deg, #f5f7fa, #e4e8f0);
            min-height: 100vh;
          }
          
          /* Conteneur principal avec ombre */
          .main-container {
            max-width: 1000px;
            margin: 40px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }
          
          /* Effet de carte pour les sections */
          .form-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
          }
          
          .form-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
          }
          
          /* Titre de section */
          .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #6930c3;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
          }
        </style>

        <!-- Ajouter cette div au début du formulaire -->
        <div class="form-header">
          <h2 class="form-title">Modifier un cours</h2>
        </div>
        
        <div class="row">
          <div class="col-md-8">
            <div class="mb-3">
              <label for="{{ form.title.id_for_label }}" class="form-label">Titre</label>
              {{ form.title }}
              {% if form.title.errors %}
                <div class="text-danger">{{ form.title.errors }}</div>
              {% endif %}
            </div>
            
            <div class="mb-3">
              <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
              {{ form.description }}
              {% if form.description.errors %}
                <div class="text-danger">{{ form.description.errors }}</div>
              {% endif %}
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.category.id_for_label }}" class="form-label">Catégorie</label>
                  {{ form.category }}
                  {% if form.category.errors %}
                    <div class="text-danger">{{ form.category.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.level.id_for_label }}" class="form-label">Niveau</label>
                  {{ form.level }}
                  {% if form.level.errors %}
                    <div class="text-danger">{{ form.level.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.instructor.id_for_label }}" class="form-label">Instructeur</label>
                  {{ form.instructor }}
                  {% if form.instructor.errors %}
                    <div class="text-danger">{{ form.instructor.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.duration.id_for_label }}" class="form-label">Durée (heures)</label>
                  {{ form.duration }}
                  {% if form.duration.errors %}
                    <div class="text-danger">{{ form.duration.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
            
            <div class="mb-3 form-check">
              {{ form.is_published }}
              <label class="form-check-label" for="{{ form.is_published.id_for_label }}">Publier le cours</label>
              {% if form.is_published.errors %}
                <div class="text-danger">{{ form.is_published.errors }}</div>
              {% endif %}
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="mb-3">
              <label for="{{ form.image.id_for_label }}" class="form-label">Image du cours</label>
              {{ form.image }}
              {% if form.image.errors %}
                <div class="text-danger">{{ form.image.errors }}</div>
              {% endif %}
            </div>
            
            <div class="card-body text-center">
              {% if form.instance.image %}
                <img src="{{ form.instance.image.url }}" class="img-fluid" style="max-height: 150px;">
              {% else %}
                <div class="text-muted">Aucune image sélectionnée</div>
              {% endif %}
            </div>
          </div>
        </div>
        
        <div class="mt-4">
          <button type="submit" class="btn btn-primary" id="enregistrer">Enregistrer</button>
          <a href="{% url 'course_list' %}" class="btn btn-secondary" id="annuler">Annuler</a>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
