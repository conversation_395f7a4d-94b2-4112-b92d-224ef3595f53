<!DOCTYPE html>
<html lang="fr" data-bs-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}E-Learn+{% endblock %}</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  {% load static %}
  <link rel="stylesheet" href="{% static 'css/style.css' %}">
  {% block extra_css %}{% endblock %}
</head>
<body>
  <header class="sticky-top">
    <nav class="navbar navbar-expand-lg navbar-light shadow-sm py-2">
      <div class="container">
        <a class="navbar-brand" href="{% url 'home' %}">
          <span style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700; font-size: 1.5rem;">E-Learn+</span>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="{% url 'home' %}">Accueil</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {% if 'courses' in request.path %}active{% endif %}" href="{% url 'course_list' %}">Cours</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {% if 'instructors' in request.path %}active{% endif %}" href="{% url 'instructor_list' %}">Instructeurs</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {% if 'about' in request.path %}active{% endif %}" href="{% url 'about' %}">À propos</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {% if 'contact' in request.path %}active{% endif %}" href="{% url 'contact' %}">Contact</a>
            </li>
          </ul>
          
          <ul class="navbar-nav">
            <!-- Bouton de changement de thème -->
            <li class="nav-item me-2">
              <button id="theme-toggle" class="btn btn-sm" style="background: var(--light); border-radius: var(--radius-pill); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                <i id="theme-icon" class="fas fa-sun"></i>
              </button>
            </li>
            
            {% if user.is_authenticated %}
              <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                  <img src="{{ user.profile.avatar.url|default:'https://via.placeholder.com/30' }}" alt="{{ user.username }}" class="rounded-circle me-1" width="30" height="30">
                  {{ user.username }}
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                  <li><a class="dropdown-item" href="{% url 'profile' %}"><i class="fas fa-user me-2"></i>Mon profil</a></li>
                  <li><a class="dropdown-item" href="{% url 'my_courses' %}"><i class="fas fa-book me-2"></i>Mes cours</a></li>
                  {% if user.is_instructor %}
                  <li><a class="dropdown-item" href="{% url 'instructor_dashboard' %}"><i class="fas fa-chalkboard-teacher me-2"></i>Espace instructeur</a></li>
                  {% endif %}
                  {% if user.is_staff %}
                  <li><a class="dropdown-item" href="{% url 'admin_dashboard' %}"><i class="fas fa-cog me-2"></i>Administration</a></li>
                  {% endif %}
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                </ul>
              </li>
            {% else %}
              <li class="nav-item">
                <a href="{% url 'login' %}" class="btn btn-outline-primary me-2" style="border-radius: var(--radius-pill); border: 1px solid var(--primary);">Connexion</a>
              </li>
              <li class="nav-item">
                <a href="{% url 'register' %}" class="btn btn-primary" style="border-radius: var(--radius-pill);">Inscription</a>
              </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>
  </header>

  <main class="py-4">
    {% block content %}{% endblock %}
  </main>

  <footer class="bg-light py-4 mt-5">
    <div class="container">
      <div class="row">
        <div class="col-md-4">
          <h5 style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700;">E-Learn+</h5>
          <p class="text-muted">Votre plateforme d'apprentissage en ligne</p>
        </div>
        <div class="col-md-4">
          <h5>Liens rapides</h5>
          <ul class="list-unstyled">
            <li><a href="{% url 'home' %}" class="text-decoration-none">Accueil</a></li>
            <li><a href="{% url 'course_list' %}" class="text-decoration-none">Cours</a></li>
            <li><a href="{% url 'about' %}" class="text-decoration-none">À propos</a></li>
            <li><a href="{% url 'contact' %}" class="text-decoration-none">Contact</a></li>
          </ul>
        </div>
        <div class="col-md-4">
          <h5>Nous contacter</h5>
          <p class="text-muted">
            <i class="fas fa-envelope me-2"></i> <EMAIL><br>
            <i class="fas fa-phone me-2"></i> +33 1 23 45 67 89
          </p>
          <div class="social-icons">
            <a href="#" class="me-2"><i class="fab fa-facebook-f"></i></a>
            <a href="#" class="me-2"><i class="fab fa-twitter"></i></a>
            <a href="#" class="me-2"><i class="fab fa-instagram"></i></a>
            <a href="#" class="me-2"><i class="fab fa-linkedin-in"></i></a>
          </div>
        </div>
      </div>
      <hr>
      <div class="text-center text-muted">
        <small>&copy; 2023 E-Learn+. Tous droits réservés.</small>
      </div>
    </div>
  </footer>

  <!-- Bootstrap JS Bundle with Popper -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Theme Toggler Script -->
  <script>
    (() => {
      'use strict'
      
      const getStoredTheme = () => localStorage.getItem('theme')
      const setStoredTheme = theme => localStorage.setItem('theme', theme)
      
      const getPreferredTheme = () => {
        const storedTheme = getStoredTheme()
        if (storedTheme) {
          return storedTheme
        }
        
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      }
      
      const setTheme = theme => {
        if (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches) {
          document.documentElement.setAttribute('data-bs-theme', 'dark')
        } else {
          document.documentElement.setAttribute('data-bs-theme', theme)
        }
      }
      
      setTheme(getPreferredTheme())
      
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
        const storedTheme = getStoredTheme()
        if (storedTheme !== 'light' && storedTheme !== 'dark') {
          setTheme(getPreferredTheme())
        }
      })
      
      window.addEventListener('DOMContentLoaded', () => {
        // Ajouter le bouton de changement de thème à la navbar si nécessaire
        const themeToggler = document.getElementById('theme-toggle')
        if (themeToggler) {
          themeToggler.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-bs-theme')
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark'
            setStoredTheme(newTheme)
            setTheme(newTheme)
            
            // Mettre à jour l'icône
            const themeIcon = document.getElementById('theme-icon')
            if (themeIcon) {
              themeIcon.className = newTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun'
            }
          })
        }
      })
    })()
  </script>
  
  {% block extra_js %}{% endblock %}
</body>
</html>
