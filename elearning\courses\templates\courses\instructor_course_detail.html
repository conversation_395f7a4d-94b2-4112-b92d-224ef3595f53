{% extends 'base.html' %}
{% load static %}

{% block title %}{{ course.title }} - Détails{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .course-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .course-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .course-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
  }

  .action-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }

  .btn-success {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
  }

  .btn-warning {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
    color: white;
  }

  .btn-danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
  }

  .btn-secondary {
    background: #6c757d;
    color: white;
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
  }

  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: #666;
    font-size: 0.9rem;
  }

  .modules-section {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .module-item {
    border: 1px solid #e9ecef;
    border-radius: 15px;
    margin-bottom: 1rem;
    overflow: hidden;
  }

  .module-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: between;
    align-items: center;
    cursor: pointer;
  }

  .module-content {
    padding: 1.5rem;
    display: none;
  }

  .module-content.show {
    display: block;
  }

  .lesson-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #f8f9fa;
  }

  .lesson-item:last-child {
    border-bottom: none;
  }

  .lesson-type {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
  }

  .type-text {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }

  .type-video {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
  }

  .type-quiz {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
  }

  .type-assignment {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
  }

  .status-published {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
  }

  .status-draft {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
    color: white;
  }

  .empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #C8A8E9;
  }
</style>
{% endblock %}

{% block content %}
<div class="course-detail-container">
  <div class="course-header">
    <div class="d-flex justify-content-between align-items-start">
      <div>
        <h1><i class="fas fa-book-open"></i> {{ course.title }}</h1>
        <p class="mb-2">{{ course.description }}</p>
        <div class="d-flex align-items-center gap-3">
          <span class="status-badge {% if course.is_published %}status-published{% else %}status-draft{% endif %}">
            {% if course.is_published %}
              <i class="fas fa-check-circle"></i> Publié
            {% else %}
              <i class="fas fa-clock"></i> Brouillon
            {% endif %}
          </span>
          <span><i class="fas fa-tag"></i> {{ course.get_category_display }}</span>
          <span><i class="fas fa-signal"></i> {{ course.get_level_display }}</span>
        </div>
      </div>
      {% if course.image %}
      <img src="{{ course.image.url }}" alt="{{ course.title }}" style="width: 150px; height: 100px; object-fit: cover; border-radius: 10px;">
      {% endif %}
    </div>
    
    <div class="course-actions">
      <a href="{% url 'instructor_edit_course' course.id %}" class="action-btn btn-warning">
        <i class="fas fa-edit"></i> Modifier
      </a>
      <a href="{% url 'instructor_course_students' course.id %}" class="action-btn btn-primary">
        <i class="fas fa-users"></i> Étudiants ({{ student_count }})
      </a>
      <form method="post" action="{% url 'instructor_toggle_course_status' course.id %}" style="display: inline;">
        {% csrf_token %}
        <button type="submit" class="action-btn {% if course.is_published %}btn-secondary{% else %}btn-success{% endif %}">
          {% if course.is_published %}
            <i class="fas fa-eye-slash"></i> Dépublier
          {% else %}
            <i class="fas fa-eye"></i> Publier
          {% endif %}
        </button>
      </form>
      <a href="{% url 'instructor_delete_course' course.id %}" class="action-btn btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce cours ?')">
        <i class="fas fa-trash"></i> Supprimer
      </a>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-number">{{ student_count }}</div>
      <div class="stat-label">Étudiants Inscrits</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ modules.count }}</div>
      <div class="stat-label">Modules</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ lesson_count }}</div>
      <div class="stat-label">Leçons</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ course.duration|default:"N/A" }}</div>
      <div class="stat-label">Durée Estimée</div>
    </div>
  </div>

  <!-- Modules et Leçons -->
  <div class="modules-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h3><i class="fas fa-list"></i> Modules et Leçons</h3>
      <a href="#" class="action-btn btn-success">
        <i class="fas fa-plus"></i> Ajouter Module
      </a>
    </div>

    {% if modules %}
      {% for module in modules %}
      <div class="module-item">
        <div class="module-header" onclick="toggleModule({{ module.id }})">
          <div>
            <h5 class="mb-1">{{ module.title }}</h5>
            <small class="text-muted">{{ module.description|truncatewords:15 }}</small>
          </div>
          <div class="d-flex align-items-center gap-2">
            <span class="badge bg-secondary">{{ module.lessons.count }} leçons</span>
            <i class="fas fa-chevron-down" id="chevron-{{ module.id }}"></i>
          </div>
        </div>
        
        <div class="module-content" id="module-{{ module.id }}">
          {% for lesson in module.lessons.all %}
          <div class="lesson-item">
            <div>
              <strong>{{ lesson.title }}</strong>
              <span class="lesson-type type-{{ lesson.content_type }}">
                {% if lesson.content_type == 'text' %}
                  <i class="fas fa-file-text"></i> Texte
                {% elif lesson.content_type == 'video' %}
                  <i class="fas fa-play"></i> Vidéo
                {% elif lesson.content_type == 'quiz' %}
                  <i class="fas fa-question-circle"></i> Quiz
                {% elif lesson.content_type == 'assignment' %}
                  <i class="fas fa-tasks"></i> Devoir
                {% endif %}
              </span>
            </div>
            <div>
              <a href="#" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-edit"></i>
              </a>
            </div>
          </div>
          {% empty %}
          <div class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-plus-circle"></i>
            </div>
            <p>Aucune leçon dans ce module</p>
            <a href="#" class="action-btn btn-success">
              <i class="fas fa-plus"></i> Ajouter une Leçon
            </a>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endfor %}
    {% else %}
    <div class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-folder-open"></i>
      </div>
      <h4>Aucun module créé</h4>
      <p>Commencez par créer des modules pour organiser votre cours</p>
      <a href="#" class="action-btn btn-success">
        <i class="fas fa-plus"></i> Créer le Premier Module
      </a>
    </div>
    {% endif %}
  </div>
</div>

<script>
function toggleModule(moduleId) {
    const content = document.getElementById('module-' + moduleId);
    const chevron = document.getElementById('chevron-' + moduleId);
    
    if (content.classList.contains('show')) {
        content.classList.remove('show');
        chevron.style.transform = 'rotate(0deg)';
    } else {
        content.classList.add('show');
        chevron.style.transform = 'rotate(180deg)';
    }
}
</script>
{% endblock %}
