# Generated manually for quiz improvements

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('quizzes', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0008_course_is_free_course_price'),
    ]

    operations = [
        # Modifier le modèle Quiz
        migrations.AddField(
            model_name='quiz',
            name='correction_type',
            field=models.CharField(choices=[('auto', 'Correction automatique'), ('manual', 'Correction manuelle')], default='auto', max_length=10),
        ),
        migrations.AddField(
            model_name='quiz',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='quiz',
            name='time_limit',
            field=models.PositiveIntegerField(blank=True, help_text='Temps limite en minutes', null=True),
        ),
        migrations.AddField(
            model_name='quiz',
            name='max_attempts',
            field=models.PositiveIntegerField(default=1, help_text='Nombre maximum de tentatives'),
        ),
        migrations.AddField(
            model_name='quiz',
            name='passing_score',
            field=models.PositiveIntegerField(default=60, help_text='Score minimum pour réussir (%)'),
        ),
        migrations.AddField(
            model_name='quiz',
            name='is_published',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='quiz',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='quiz',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='quiz',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='created_quizzes', to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        
        # Modifier le modèle Question
        migrations.AddField(
            model_name='question',
            name='question_type',
            field=models.CharField(choices=[('multiple_choice', 'Choix multiple'), ('true_false', 'Vrai/Faux'), ('short_answer', 'Réponse courte'), ('essay', 'Dissertation')], default='multiple_choice', max_length=20),
        ),
        migrations.AddField(
            model_name='question',
            name='points',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name='question',
            name='order',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='question',
            name='correct_answer_boolean',
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='question',
            name='correct_answer_text',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='question',
            name='explanation',
            field=models.TextField(blank=True, help_text='Explication de la bonne réponse'),
        ),
        migrations.AddField(
            model_name='question',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='question',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        
        # Modifier les champs existants
        migrations.AlterField(
            model_name='quiz',
            name='course',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to='courses.course'),
        ),
        migrations.AlterField(
            model_name='question',
            name='quiz',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='quizzes.quiz'),
        ),
        migrations.AlterField(
            model_name='question',
            name='option_a',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='question',
            name='option_b',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='question',
            name='option_c',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='question',
            name='option_d',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='question',
            name='correct_option',
            field=models.CharField(blank=True, choices=[('A', 'A'), ('B', 'B'), ('C', 'C'), ('D', 'D')], max_length=1),
        ),
        
        # Supprimer l'ancien modèle QuizSubmission et le recréer
        migrations.DeleteModel(
            name='QuizSubmission',
        ),
        
        # Créer les nouveaux modèles
        migrations.CreateModel(
            name='QuizSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('in_progress', 'En cours'), ('submitted', 'Soumis'), ('graded', 'Corrigé')], default='in_progress', max_length=20)),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('max_score', models.PositiveIntegerField(default=0)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('attempt_number', models.PositiveIntegerField(default=1)),
                ('time_taken', models.DurationField(blank=True, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('graded_at', models.DateTimeField(blank=True, null=True)),
                ('feedback', models.TextField(blank=True)),
                ('graded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_submissions', to=settings.AUTH_USER_MODEL)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='quizzes.quiz')),
                ('student', models.ForeignKey(limit_choices_to={'role': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='quiz_submissions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        
        migrations.CreateModel(
            name='QuizAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('selected_option', models.CharField(blank=True, max_length=1)),
                ('boolean_answer', models.BooleanField(blank=True, null=True)),
                ('text_answer', models.TextField(blank=True)),
                ('is_correct', models.BooleanField(blank=True, null=True)),
                ('points_awarded', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('instructor_feedback', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quizzes.question')),
                ('submission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='quizzes.quizsubmission')),
            ],
        ),
        
        # Ajouter les contraintes
        migrations.AlterUniqueTogether(
            name='quizsubmission',
            unique_together={('quiz', 'student', 'attempt_number')},
        ),
        migrations.AlterUniqueTogether(
            name='quizanswer',
            unique_together={('submission', 'question')},
        ),
    ]
