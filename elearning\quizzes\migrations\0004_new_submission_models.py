# Generated manually for new submission models

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('quizzes', '0003_quiz_improvements'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # Supprimer l'ancien modèle QuizSubmission s'il existe
        migrations.DeleteModel(
            name='QuizSubmission',
        ),
        
        # Créer les nouveaux modèles
        migrations.CreateModel(
            name='QuizSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('in_progress', 'En cours'), ('submitted', 'Soumis'), ('graded', 'Corrigé')], default='in_progress', max_length=20)),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('max_score', models.PositiveIntegerField(default=0)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('attempt_number', models.PositiveIntegerField(default=1)),
                ('time_taken', models.DurationField(blank=True, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('graded_at', models.DateTimeField(blank=True, null=True)),
                ('feedback', models.TextField(blank=True)),
                ('graded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_submissions', to=settings.AUTH_USER_MODEL)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='quizzes.quiz')),
                ('student', models.ForeignKey(limit_choices_to={'role': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='quiz_submissions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        
        migrations.CreateModel(
            name='QuizAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('selected_option', models.CharField(blank=True, max_length=1)),
                ('boolean_answer', models.BooleanField(blank=True, null=True)),
                ('text_answer', models.TextField(blank=True)),
                ('is_correct', models.BooleanField(blank=True, null=True)),
                ('points_awarded', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('instructor_feedback', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quizzes.question')),
                ('submission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='quizzes.quizsubmission')),
            ],
        ),
        
        # Ajouter les contraintes
        migrations.AlterUniqueTogether(
            name='quizsubmission',
            unique_together={('quiz', 'student', 'attempt_number')},
        ),
        migrations.AlterUniqueTogether(
            name='quizanswer',
            unique_together={('submission', 'question')},
        ),
    ]
