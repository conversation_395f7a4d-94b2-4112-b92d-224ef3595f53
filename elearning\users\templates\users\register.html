{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block extra_css %}
<style>
  /* Container principal moderne */
  .register-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    padding: 40px 20px;
  }

  .register-form-container {
    max-width: 500px;
    width: 100%;
    background: white;
    border-radius: 24px;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
  }

  /* En-tête avec dégradé moderne */
  .register-form-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .register-form-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }

  .register-form-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    position: relative;
    z-index: 2;
  }

  .register-form-title i {
    font-size: 2.5rem;
    margin-right: 15px;
    opacity: 0.9;
  }

  .register-form-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    position: relative;
    z-index: 2;
  }

  /* Corps du formulaire */
  .form-body {
    padding: 40px;
  }

  /* Bouton de soumission moderne */
  .btn-register {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    width: 100%;
  }

  .btn-register:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    color: white;
  }

  .btn-register:active {
    transform: translateY(-1px);
  }

  .btn-register i {
    margin-right: 10px;
  }

  /* Lien de connexion */
  .login-link {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(102, 126, 234, 0.1);
  }

  .login-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
  }

  .login-link a:hover {
    color: #764ba2;
    text-decoration: underline;
  }
</style>
{% endblock %}

{% block content %}
<div class="register-container">
  <div class="register-form-container">
    <div class="register-form-header">
      <h1 class="register-form-title">
        <i class="fas fa-user-plus"></i>
        Inscription
      </h1>
      <p>Créez votre compte pour accéder à la plateforme</p>
    </div>

    <div class="form-body">
      <form method="post" novalidate>
        {% csrf_token %}
        {{ form|crispy }}
        <div class="d-grid mt-4">
          <button type="submit" class="btn-register">
            <i class="fas fa-user-plus"></i>
            S'inscrire
          </button>
        </div>
      </form>

      <div class="login-link">
        <p>Vous avez déjà un compte ? <a href="{% url 'login' %}">Connectez-vous ici</a></p>
      </div>
    </div>
  </div>
</div>
{% endblock %}
