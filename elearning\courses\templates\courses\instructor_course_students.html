{% extends 'base.html' %}
{% load static %}

{% block title %}Étudiants - {{ course.title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .students-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .students-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .back-btn {
    background: white;
    color: #333;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
  }

  .back-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #333;
    text-decoration: none;
  }

  .search-section {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .search-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    width: 100%;
    transition: all 0.3s ease;
  }

  .search-input:focus {
    border-color: #C8A8E9;
    box-shadow: 0 0 0 0.2rem rgba(200, 168, 233, 0.25);
  }

  .students-table {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
  }

  .table th {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    font-weight: 600;
    padding: 1rem;
  }

  .table td {
    padding: 1rem;
    vertical-align: middle;
  }

  .student-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-weight: bold;
    font-size: 1.2rem;
  }

  .progress-bar-container {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  .progress-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
  }

  .status-active {
    color: #28a745;
    font-weight: bold;
  }

  .status-inactive {
    color: #dc3545;
    font-weight: bold;
  }

  .action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }

  .btn-success {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
  }

  .stats-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
  }

  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: #666;
    font-size: 0.9rem;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #C8A8E9;
  }
</style>
{% endblock %}

{% block content %}
<div class="students-container">
  <div class="students-header">
    <a href="{% url 'instructor_course_detail' course.id %}" class="back-btn">
      <i class="fas fa-arrow-left"></i> Retour au Cours
    </a>
    <h1><i class="fas fa-users"></i> Étudiants - {{ course.title }}</h1>
    <p>Suivez la progression de vos étudiants</p>
  </div>

  <!-- Statistiques résumées -->
  <div class="stats-summary">
    <div class="stat-card">
      <div class="stat-number">{{ students.count }}</div>
      <div class="stat-label">Étudiants Inscrits</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">
        {% widthratio students|length 1 1 %}
      </div>
      <div class="stat-label">Étudiants Actifs</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">
        {% if students %}
          {{ students|first.progress_rate|default:0 }}%
        {% else %}
          0%
        {% endif %}
      </div>
      <div class="stat-label">Progression Moyenne</div>
    </div>
  </div>

  <!-- Barre de recherche -->
  <div class="search-section">
    <div class="row align-items-center">
      <div class="col-md-8">
        <input type="text" id="searchInput" class="search-input" placeholder="🔍 Rechercher un étudiant...">
      </div>
      <div class="col-md-4 text-end">
        <span class="text-muted">Total: {{ students.count }} étudiants</span>
      </div>
    </div>
  </div>

  <!-- Tableau des étudiants -->
  {% if students %}
  <div class="students-table">
    <table class="table table-hover mb-0" id="studentsTable">
      <thead>
        <tr>
          <th><i class="fas fa-user"></i> Étudiant</th>
          <th><i class="fas fa-envelope"></i> Email</th>
          <th><i class="fas fa-chart-line"></i> Progression</th>
          <th><i class="fas fa-check-circle"></i> Leçons Complétées</th>
          <th><i class="fas fa-calendar"></i> Inscription</th>
          <th><i class="fas fa-cogs"></i> Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for student in students %}
        <tr>
          <td>
            <div class="d-flex align-items-center">
              <div class="student-avatar me-3">
                {{ student.username|first|upper }}
              </div>
              <div>
                <strong>{{ student.username }}</strong>
                {% if student.first_name or student.last_name %}
                <br><small class="text-muted">{{ student.first_name }} {{ student.last_name }}</small>
                {% endif %}
              </div>
            </div>
          </td>
          <td>{{ student.email }}</td>
          <td>
            <div class="progress-bar-container">
              <div class="progress-bar" style="width: {{ student.progress_rate|default:0 }}%"></div>
            </div>
            <div class="progress-text">{{ student.progress_rate|default:0 }}% complété</div>
          </td>
          <td>
            <strong>{{ student.completed_lessons|default:0 }}</strong>
            <small class="text-muted">/ {{ course.total_lessons|default:0 }}</small>
          </td>
          <td>
            <small>{{ student.date_joined|date:"d/m/Y" }}</small>
          </td>
          <td>
            <div class="d-flex gap-1">
              <a href="#" class="action-btn btn-primary" title="Voir le profil">
                <i class="fas fa-eye"></i>
              </a>
              <a href="#" class="action-btn btn-success" title="Envoyer un message">
                <i class="fas fa-envelope"></i>
              </a>
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% else %}
  <div class="empty-state">
    <div class="empty-icon">
      <i class="fas fa-user-graduate"></i>
    </div>
    <h3>Aucun étudiant inscrit</h3>
    <p>Les étudiants apparaîtront ici une fois qu'ils se seront inscrits à votre cours</p>
    {% if not course.is_published %}
    <p class="text-muted">
      <i class="fas fa-info-circle"></i>
      Votre cours doit être publié pour que les étudiants puissent s'y inscrire
    </p>
    {% endif %}
  </div>
  {% endif %}
</div>

<script>
// Fonction de recherche
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#studentsTable tbody tr');
    
    tableRows.forEach(row => {
        const username = row.cells[0].textContent.toLowerCase();
        const email = row.cells[1].textContent.toLowerCase();
        
        if (username.includes(searchTerm) || email.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
