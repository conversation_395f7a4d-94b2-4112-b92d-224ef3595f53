#!/usr/bin/env python
"""
Script pour appliquer toutes les migrations manquantes
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

def main():
    print("🚀 APPLICATION DE TOUTES LES MIGRATIONS")
    print("=" * 50)
    
    try:
        # Vérifier les migrations en attente
        print("📋 Vérification des migrations en attente...")
        execute_from_command_line(['manage.py', 'showmigrations'])
        
        print("\n🔄 Application de toutes les migrations...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("\n✅ TOUTES LES MIGRATIONS APPLIQUÉES AVEC SUCCÈS!")
        
        # Créer des données de test
        print("\n📊 Création des données de test...")
        create_test_data()
        
        print("\n🎉 CONFIGURATION TERMINÉE!")
        print("\nVous pouvez maintenant:")
        print("1. Démarrer le serveur: python manage.py runserver")
        print("2. Accéder au dashboard instructeur")
        print("3. Tester les fonctionnalités de paiement et quiz")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        sys.exit(1)

def create_test_data():
    """Créer des données de test"""
    from users.models import User
    from courses.models import Course
    from decimal import Decimal
    
    try:
        # Créer un instructeur de test
        instructor, created = User.objects.get_or_create(
            username='prof_test',
            defaults={
                'email': '<EMAIL>',
                'role': 'instructor',
                'first_name': 'Professeur',
                'last_name': 'Test'
            }
        )
        if created:
            instructor.set_password('test123')
            instructor.save()
            print(f"✅ Instructeur créé: {instructor.username}")
        
        # Créer un étudiant de test
        student, created = User.objects.get_or_create(
            username='etudiant_test',
            defaults={
                'email': '<EMAIL>',
                'role': 'student',
                'first_name': 'Étudiant',
                'last_name': 'Test'
            }
        )
        if created:
            student.set_password('test123')
            student.save()
            print(f"✅ Étudiant créé: {student.username}")
        
        # Mettre à jour les cours existants avec des prix
        courses = Course.objects.all()
        for course in courses:
            if not hasattr(course, 'price') or course.price is None:
                course.is_free = True
                course.price = Decimal('0.00')
                course.save()
                print(f"✅ Cours mis à jour: {course.title} (Gratuit)")
        
        # Créer des cours de test avec prix
        test_courses = [
            {
                'title': 'Python Avancé - Payant',
                'description': 'Cours Python avancé avec certification',
                'category': 'programming',
                'level': 'advanced',
                'is_free': False,
                'price': Decimal('49.99')
            },
            {
                'title': 'JavaScript Gratuit',
                'description': 'Introduction gratuite à JavaScript',
                'category': 'programming',
                'level': 'beginner',
                'is_free': True,
                'price': Decimal('0.00')
            }
        ]
        
        for course_data in test_courses:
            course, created = Course.objects.get_or_create(
                title=course_data['title'],
                defaults={
                    **course_data,
                    'instructor': instructor,
                    'is_published': True
                }
            )
            if created:
                print(f"✅ Cours créé: {course.title} ({'Gratuit' if course.is_free else f'{course.price}€'})")
        
    except Exception as e:
        print(f"⚠️ Erreur lors de la création des données de test: {e}")

if __name__ == '__main__':
    main()
