document.addEventListener('DOMContentLoaded', function() {
  // Animation d'entrée pour les cartes de statistiques
  const statCards = document.querySelectorAll('.stat-card');
  statCards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    setTimeout(() => {
      card.style.transition = 'all 0.5s ease';
      card.style.opacity = '1';
      card.style.transform = 'translateY(0)';
    }, 100 * index);
  });

  // Compteurs animés pour les statistiques
  const numberElements = document.querySelectorAll('.number');
  numberElements.forEach(element => {
    const textContent = element.textContent.trim();
    const finalValue = !isNaN(parseInt(textContent)) ? parseInt(textContent) : 0;
    
    let startValue = 0;
    const duration = 1500;
    const increment = finalValue / (duration / 20);
    
    function updateCount() {
      startValue += increment;
      if (startValue < finalValue) {
        element.textContent = Math.floor(startValue);
        setTimeout(updateCount, 20);
      } else {
        element.textContent = finalValue;
      }
    }
    
    if (finalValue > 0) {
      updateCount();
    }
  });

  // Graphique de répartition des utilisateurs
  const userDistributionCtx = document.getElementById('userDistributionChart');
  if (userDistributionCtx) {
    new Chart(userDistributionCtx, {
      type: 'doughnut',
      data: {
        labels: ['Étudiants', 'Instructeurs', 'Administrateurs'],
        datasets: [{
          data: [
            document.querySelector('[data-student-count]')?.dataset.studentCount || 0,
            document.querySelector('[data-instructor-count]')?.dataset.instructorCount || 0,
            document.querySelector('[data-admin-count]')?.dataset.adminCount || 0
          ],
          backgroundColor: ['#4361ee', '#f72585', '#4cc9f0'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        cutoutPercentage: 70,
        legend: {
          position: 'bottom'
        },
        animation: {
          animateScale: true,
          animateRotate: true
        }
      }
    });
  }

  // Graphique des inscriptions mensuelles
  const monthlyRegistrationsCtx = document.getElementById('monthlyRegistrationsChart');
  if (monthlyRegistrationsCtx) {
    new Chart(monthlyRegistrationsCtx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
        datasets: [{
          label: 'Inscriptions',
          data: [12, 19, 3, 5, 2, 3, 20, 33, 23, 12, 5, 6],
          backgroundColor: 'rgba(67, 97, 238, 0.1)',
          borderColor: '#4361ee',
          borderWidth: 3,
          pointBackgroundColor: '#fff',
          pointBorderColor: '#4361ee',
          pointRadius: 4,
          tension: 0.3,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Graphique de progression des cours
  const courseProgressCtx = document.getElementById('courseProgressChart');
  if (courseProgressCtx) {
    new Chart(courseProgressCtx, {
      type: 'bar',
      data: {
        labels: ['Python', 'JavaScript', 'Java', 'C++', 'PHP', 'Ruby'],
        datasets: [{
          label: 'Étudiants inscrits',
          data: [65, 59, 80, 81, 56, 55],
          backgroundColor: '#4361ee'
        }, {
          label: 'Cours complétés',
          data: [28, 48, 40, 19, 36, 27],
          backgroundColor: '#4cc9f0'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Graphique d'activité hebdomadaire
  const weeklyActivityCtx = document.getElementById('weeklyActivityChart');
  if (weeklyActivityCtx) {
    new Chart(weeklyActivityCtx, {
      type: 'line',
      data: {
        labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
        datasets: [{
          label: 'Connexions',
          data: [30, 45, 61, 48, 53, 33, 25],
          borderColor: '#f72585',
          backgroundColor: 'rgba(247, 37, 133, 0.1)',
          borderWidth: 3,
          pointBackgroundColor: '#fff',
          pointBorderColor: '#f72585',
          pointRadius: 4,
          tension: 0.3,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }
});

