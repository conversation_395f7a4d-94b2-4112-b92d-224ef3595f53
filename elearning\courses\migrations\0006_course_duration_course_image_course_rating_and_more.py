# Generated by Django 5.2.1 on 2025-06-10 18:47

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0005_remove_course_duration_remove_course_image_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='course',
            name='duration',
            field=models.CharField(default='8 heures', max_length=50),
        ),
        migrations.AddField(
            model_name='course',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='courses/'),
        ),
        migrations.AddField(
            model_name='course',
            name='rating',
            field=models.DecimalField(decimal_places=1, default=4.5, max_digits=3),
        ),
        migrations.AddField(
            model_name='course',
            name='students',
            field=models.ManyToManyField(blank=True, related_name='enrolled_courses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='course',
            name='category',
            field=models.CharField(choices=[('programming', 'Programmation'), ('design', 'Design'), ('business', 'Business'), ('marketing', 'Marketing'), ('personal', 'Développement personnel'), ('other', 'Autre')], default='other', max_length=50),
        ),
    ]
