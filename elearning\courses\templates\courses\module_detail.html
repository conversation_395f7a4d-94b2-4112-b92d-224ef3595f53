{% extends 'base.html' %}
{% load static %}

{% block title %}{{ module.title }} | E-Learn+{% endblock %}

{% block extra_css %}
<style>
  .lesson-card {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    margin-bottom: 15px;
  }
  
  .lesson-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  }
  
  .lesson-card.completed {
    border-left: 4px solid #28a745;
  }
  
  .module-header {
    background: linear-gradient(135deg, #4361ee, #3a0ca3);
    padding: 40px 0;
    margin-bottom: 40px;
    color: white;
  }
  
  .lesson-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f8f9fa;
    margin-right: 15px;
  }
  
  .lesson-icon.completed {
    background-color: #d4edda;
    color: #28a745;
  }
  
  .lesson-title {
    font-weight: 600;
    margin-bottom: 0;
    font-size: 1.1rem;
    color: #333;
  }
  
  .lesson-duration {
    font-size: 0.85rem;
    color: #6c757d;
  }
  
  .nav-pills .nav-link.active {
    background-color: #4361ee;
  }
</style>
{% endblock %}

{% block content %}
<!-- En-tête du module -->
<div class="module-header">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-md-8">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb mb-3">
            <li class="breadcrumb-item"><a href="{% url 'course_list' %}" class="text-white">Cours</a></li>
            <li class="breadcrumb-item"><a href="{% url 'course_detail' course.id %}" class="text-white">{{ course.title }}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'module_list' course.id %}" class="text-white">Modules</a></li>
            <li class="breadcrumb-item active text-white-50" aria-current="page">{{ module.title }}</li>
          </ol>
        </nav>
        <h1 class="display-5 fw-bold mb-3">{{ module.title }}</h1>
        {% if module.description %}
        <p class="lead mb-0">{{ module.description }}</p>
        {% endif %}
      </div>
      <div class="col-md-4 text-md-end mt-4 mt-md-0">
        <a href="{% url 'module_list' course.id %}" class="btn btn-light btn-lg px-4">
          <i class="bi bi-arrow-left me-2"></i>Retour aux modules
        </a>
      </div>
    </div>
  </div>
</div>

<div class="container mb-5">
  <div class="row">
    <div class="col-lg-8">
      <h2 class="mb-4">Leçons du module</h2>
      
      {% for lesson in lessons %}
      <div class="lesson-card p-3 {% if forloop.counter == 1 %}completed{% endif %}">
        <div class="d-flex align-items-center">
          <div class="lesson-icon {% if forloop.counter == 1 %}completed{% endif %}">
            {% if forloop.counter == 1 %}
            <i class="bi bi-check-lg"></i>
            {% else %}
            <i class="bi bi-play-fill"></i>
            {% endif %}
          </div>
          <div class="flex-grow-1">
            <div class="d-flex justify-content-between align-items-center">
              <h3 class="lesson-title">{{ lesson.title }}</h3>
              <span class="lesson-duration"><i class="bi bi-clock me-1"></i>15 min</span>
            </div>
            {% if lesson.video_url %}
            <span class="badge bg-info text-dark me-2"><i class="bi bi-camera-video me-1"></i>Vidéo</span>
            {% endif %}
            <span class="badge bg-light text-dark"><i class="bi bi-file-text me-1"></i>Lecture</span>
          </div>
          <div class="ms-3">
            <a href="{% url 'lesson_detail' course.id module.id lesson.id %}" class="btn btn-sm btn-primary">
              {% if forloop.counter == 1 %}
              Revoir
              {% else %}
              Commencer
              {% endif %}
            </a>
          </div>
        </div>
      </div>
      {% empty %}
      <div class="text-center py-5">
        <i class="bi bi-journal-x display-1 text-muted"></i>
        <h3 class="mt-4">Aucune leçon disponible</h3>
        <p class="text-muted">Ce module ne contient pas encore de leçons.</p>
      </div>
      {% endfor %}
    </