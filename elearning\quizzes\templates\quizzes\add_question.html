{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
  .question-form-container {
    max-width: 900px;
    margin: 2rem auto;
    padding: 2rem;
    background: linear-gradient(135deg, #C8A8E9 0%, #A8D0F0 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  }

  .form-card {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
  }

  .form-title {
    color: #6B46C1;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .quiz-info {
    background: #F3F4F6;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
  }

  .form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #E5E7EB;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
  }

  .form-control:focus {
    outline: none;
    border-color: #C8A8E9;
    box-shadow: 0 0 0 3px rgba(200, 168, 233, 0.1);
  }

  .form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  .question-type-section {
    background: #F9FAFB;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .options-container {
    display: none;
  }

  .options-container.active {
    display: block;
  }

  .option-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .option-label {
    min-width: 40px;
    font-weight: 600;
    color: #6B46C1;
  }

  .option-input {
    flex: 1;
  }

  .correct-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .correct-radio {
    width: 1.25rem;
    height: 1.25rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #6B46C1, #9333EA);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(107, 70, 193, 0.3);
  }

  .btn-secondary {
    background: #6B7280;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-secondary:hover {
    background: #4B5563;
    transform: translateY(-2px);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
  }

  .help-text {
    font-size: 0.875rem;
    color: #6B7280;
    margin-top: 0.25rem;
  }

  .true-false-options {
    display: flex;
    gap: 2rem;
    justify-content: center;
    margin-top: 1rem;
  }

  .true-false-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: 2px solid #E5E7EB;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .true-false-option:hover {
    border-color: #C8A8E9;
  }

  .true-false-option.selected {
    border-color: #6B46C1;
    background: rgba(107, 70, 193, 0.1);
  }

  .true-false-option input[type="radio"] {
    width: 1.25rem;
    height: 1.25rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="question-form-container">
  <div class="form-card">
    <h1 class="form-title">
      <i class="fas fa-question-circle"></i>
      Ajouter une question
    </h1>
    
    <div class="quiz-info">
      <strong>Quiz:</strong> {{ quiz.title }}
      <br>
      <small class="text-muted">{{ quiz.get_correction_type_display }}</small>
    </div>

    <form method="post" id="question-form">
      {% csrf_token %}
      
      <div class="form-group">
        <label for="question_type" class="form-label">
          <i class="fas fa-list"></i> Type de question *
        </label>
        <select id="question_type" name="question_type" class="form-control form-select" required>
          <option value="multiple_choice">Choix multiple (QCM)</option>
          <option value="true_false">Vrai/Faux</option>
          <option value="short_answer">Réponse courte</option>
          <option value="essay">Dissertation</option>
        </select>
      </div>

      <div class="form-group">
        <label for="text" class="form-label">
          <i class="fas fa-question"></i> Question *
        </label>
        <textarea id="text" name="text" class="form-control" rows="3" required 
                  placeholder="Saisissez votre question ici..."></textarea>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="points" class="form-label">
              <i class="fas fa-star"></i> Points *
            </label>
            <input type="number" id="points" name="points" class="form-control" 
                   value="1" min="1" max="10" required>
          </div>
        </div>
      </div>

      <!-- Options pour QCM -->
      <div id="multiple-choice-options" class="options-container question-type-section active">
        <h3><i class="fas fa-list-ul"></i> Options de réponse</h3>
        
        <div class="option-group">
          <div class="option-label">A)</div>
          <input type="text" name="option_a" class="form-control option-input" 
                 placeholder="Première option" required>
          <div class="correct-indicator">
            <input type="radio" name="correct_option" value="A" class="correct-radio" required>
            <label>Correcte</label>
          </div>
        </div>

        <div class="option-group">
          <div class="option-label">B)</div>
          <input type="text" name="option_b" class="form-control option-input" 
                 placeholder="Deuxième option" required>
          <div class="correct-indicator">
            <input type="radio" name="correct_option" value="B" class="correct-radio">
            <label>Correcte</label>
          </div>
        </div>

        <div class="option-group">
          <div class="option-label">C)</div>
          <input type="text" name="option_c" class="form-control option-input" 
                 placeholder="Troisième option" required>
          <div class="correct-indicator">
            <input type="radio" name="correct_option" value="C" class="correct-radio">
            <label>Correcte</label>
          </div>
        </div>

        <div class="option-group">
          <div class="option-label">D)</div>
          <input type="text" name="option_d" class="form-control option-input" 
                 placeholder="Quatrième option" required>
          <div class="correct-indicator">
            <input type="radio" name="correct_option" value="D" class="correct-radio">
            <label>Correcte</label>
          </div>
        </div>
      </div>

      <!-- Options pour Vrai/Faux -->
      <div id="true-false-options" class="options-container question-type-section">
        <h3><i class="fas fa-check-circle"></i> Réponse correcte</h3>
        
        <div class="true-false-options">
          <div class="true-false-option" onclick="selectTrueFalse('true')">
            <input type="radio" name="correct_answer_boolean" value="true" id="answer-true">
            <label for="answer-true">
              <i class="fas fa-check text-success"></i>
              <strong>Vrai</strong>
            </label>
          </div>
          
          <div class="true-false-option" onclick="selectTrueFalse('false')">
            <input type="radio" name="correct_answer_boolean" value="false" id="answer-false">
            <label for="answer-false">
              <i class="fas fa-times text-danger"></i>
              <strong>Faux</strong>
            </label>
          </div>
        </div>
      </div>

      <!-- Options pour réponse courte/dissertation -->
      <div id="text-answer-options" class="options-container question-type-section">
        <h3><i class="fas fa-edit"></i> Réponse attendue</h3>
        
        <div class="form-group">
          <label for="correct_answer_text" class="form-label">
            Réponse correcte (optionnel pour correction manuelle)
          </label>
          <textarea id="correct_answer_text" name="correct_answer_text" class="form-control" rows="3"
                    placeholder="Saisissez la réponse attendue..."></textarea>
          <div class="help-text">
            Pour les questions à correction automatique, cette réponse sera comparée exactement avec celle de l'étudiant.
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="explanation" class="form-label">
          <i class="fas fa-lightbulb"></i> Explication (optionnel)
        </label>
        <textarea id="explanation" name="explanation" class="form-control" rows="2"
                  placeholder="Explication de la réponse correcte..."></textarea>
        <div class="help-text">
          Cette explication sera affichée aux étudiants après qu'ils aient répondu à la question.
        </div>
      </div>

      <div class="form-actions">
        <a href="{% url 'quizzes:edit_quiz' quiz.id %}" class="btn-secondary">
          <i class="fas fa-arrow-left"></i>
          Retour au quiz
        </a>
        <button type="submit" class="btn-primary">
          <i class="fas fa-save"></i>
          Ajouter la question
        </button>
      </div>
    </form>
  </div>
</div>

<script>
document.getElementById('question_type').addEventListener('change', function() {
  const questionType = this.value;

  // Masquer toutes les options
  const optionsContainers = document.querySelectorAll('.options-container');
  optionsContainers.forEach(container => {
    container.classList.remove('active');
  });

  // Afficher les options appropriées
  if (questionType === 'multiple_choice') {
    document.getElementById('multiple-choice-options').classList.add('active');
  } else if (questionType === 'true_false') {
    document.getElementById('true-false-options').classList.add('active');
  } else if (questionType === 'short_answer' || questionType === 'essay') {
    document.getElementById('text-answer-options').classList.add('active');
  }
});

function selectTrueFalse(value) {
  // Désélectionner toutes les options
  const options = document.querySelectorAll('.true-false-option');
  options.forEach(option => option.classList.remove('selected'));

  // Sélectionner l'option cliquée
  event.currentTarget.classList.add('selected');

  // Cocher le radio button correspondant
  document.querySelector(`input[value="${value}"]`).checked = true;
}

// Validation du formulaire
document.getElementById('question-form').addEventListener('submit', function(e) {
  const questionType = document.getElementById('question_type').value;

  if (questionType === 'multiple_choice') {
    const correctOption = document.querySelector('input[name="correct_option"]:checked');
    if (!correctOption) {
      e.preventDefault();
      alert('Veuillez sélectionner la réponse correcte pour le QCM.');
      return;
    }
  } else if (questionType === 'true_false') {
    const correctAnswer = document.querySelector('input[name="correct_answer_boolean"]:checked');
    if (!correctAnswer) {
      e.preventDefault();
      alert('Veuillez sélectionner Vrai ou Faux comme réponse correcte.');
      return;
    }
  }
});
</script>
{% endblock %}
