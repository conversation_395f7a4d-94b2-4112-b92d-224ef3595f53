# Generated by Django 5.2.1 on 2025-06-10 18:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0004_alter_course_category_alter_course_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='course',
            name='duration',
        ),
        migrations.RemoveField(
            model_name='course',
            name='image',
        ),
        migrations.RemoveField(
            model_name='course',
            name='rating',
        ),
        migrations.RemoveField(
            model_name='course',
            name='students',
        ),
        migrations.AddField(
            model_name='course',
            name='is_published',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='course',
            name='level',
            field=models.CharField(choices=[('beginner', 'Débutant'), ('intermediate', 'Intermédiaire'), ('advanced', 'Avancé')], default='beginner', max_length=20),
        ),
        migrations.AlterField(
            model_name='course',
            name='category',
            field=models.CharField(choices=[('programming', 'Programmation'), ('design', 'Design'), ('business', 'Business'), ('marketing', 'Marketing'), ('photography', 'Photographie'), ('music', 'Musique'), ('other', 'Autre')], default='other', max_length=50),
        ),
        migrations.AlterField(
            model_name='course',
            name='instructor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='course',
            name='slug',
            field=models.SlugField(blank=True, max_length=200, unique=True),
        ),
    ]
