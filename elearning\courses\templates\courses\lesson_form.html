{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %}{{ action }} Lesson{% endblock %}
{% block content %}
<div class="container py-4">
  <h3>{{ action }} Lesson in Module: {{ module.title }}</h3>
  <form method="post">
    {% csrf_token %}
    {{ form|crispy }}
    <button type="submit" class="btn btn-success">{{ action }}</button>
    <a href="{% url 'manage_lessons' module.id %}" class="btn btn-secondary">Cancel</a>
  </form>
</div>
{% endblock %}