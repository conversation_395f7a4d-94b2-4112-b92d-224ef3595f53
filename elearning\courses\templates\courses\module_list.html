{% extends 'base.html' %}
{% load static %}

{% block title %}Modules - {{ course.title }} | E-Learn+{% endblock %}

{% block extra_css %}
<style>
  .module-card {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    margin-bottom: 20px;
  }
  
  .module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
  }
  
  .module-header {
    background: linear-gradient(135deg, #4361ee, #3a0ca3);
    padding: 60px 0;
    margin-bottom: 50px;
    color: white;
  }
  
  .module-title {
    font-weight: 700;
    margin-bottom: 10px;
    font-size: 1.2rem;
    color: #333;
  }
  
  .module-progress {
    height: 8px;
    border-radius: 4px;
    margin-top: 15px;
  }
  
  .module-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
  }
</style>
{% endblock %}

{% block content %}
<!-- En-tête du cours -->
<div class="module-header">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="display-5 fw-bold mb-3">{{ course.title }}</h1>
        <p class="lead mb-4">{{ course.description }}</p>
        <div class="d-flex align-items-center">
          <div class="me-4">
            <i class="bi bi-clock me-2"></i>{{ course.duration|default:"8h" }}
          </div>
          <div class="me-4">
            <i class="bi bi-people me-2"></i>{{ course.students.count|default:"0" }} étudiants
          </div>
          <div>
            <i class="bi bi-star-fill me-2 text-warning"></i>{{ course.rating|default:"4.5" }}
          </div>
        </div>
      </div>
      <div class="col-md-4 text-md-end mt-4 mt-md-0">
        <a href="{% url 'course_detail' course.id %}" class="btn btn-light btn-lg px-4 me-2">
          <i class="bi bi-arrow-left me-2"></i>Retour au cours
        </a>
      </div>
    </div>
  </div>
</div>

<div class="container mb-5">
  <div class="row">
    <div class="col-lg-8">
      <h2 class="mb-4">Modules du cours</h2>
      
      {% for module in modules %}
      <div class="module-card p-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h3 class="module-title mb-0">{{ module.title }}</h3>
          <span class="badge bg-primary">Module {{ forloop.counter }}</span>
        </div>
        
        {% if module.description %}
        <p class="text-muted mb-3">{{ module.description }}</p>
        {% endif %}
        
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <span class="text-muted">{{ module.lessons.count }} leçons</span>
          </div>
          <a href="{% url 'module_detail' course.id module.id %}" class="btn btn-primary">
            Voir les leçons
          </a>
        </div>
        
        <div class="progress module-progress">
          <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
      {% empty %}
      <div class="text-center py-5">
        <i class="bi bi-journal-x display-1 text-muted"></i>
        <h3 class="mt-4">Aucun module disponible</h3>
        <p class="text-muted">Ce cours ne contient pas encore de modules.</p>
      </div>
      {% endfor %}
    </div>
    
    <div class="col-lg-4">
      <div class="card shadow-sm sticky-top" style="top: 20px;">
        <div class="card-header bg-white">
          <h4 class="mb-0">Votre progression</h4>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between mb-3">
            <span>Progression globale</span>
            <span class="fw-bold">25%</span>
          </div>
          <div class="progress mb-4" style="height: 10px;">
            <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
          
          <div class="d-flex justify-content-between align-items-center mb-3">
            <span>Modules complétés</span>
            <span class="badge bg-success">1/{{ modules.count }}</span>
          </div>
          
          <div class="d-flex justify-content-between align-items-center">
            <span>Leçons complétées</span>
            <span class="badge bg-success">3/12</span>
          </div>
          
          <hr>
          
          <div class="d-grid">
            <a href="#" class="btn btn-outline-primary">
              <i class="bi bi-trophy me-2"></i>Voir votre certificat
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
