# Generated manually for price fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0007_lesson_content_type_lesson_created_at_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='course',
            name='is_free',
            field=models.BooleanField(default=False, help_text='Cours gratuit'),
        ),
        migrations.AddField(
            model_name='course',
            name='price',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Prix du cours en euros', max_digits=10),
        ),
    ]
