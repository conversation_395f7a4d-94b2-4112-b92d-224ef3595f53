{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} | E-Learn+{% endblock %}

{% block content %}
<div class="container py-4">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
      <li class="breadcrumb-item"><a href="{% url 'admin_courses' %}">Cours</a></li>
      <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
  </nav>

  <div class="card shadow-sm">
    <div class="card-header bg-white">
      <h5 class="mb-0">{{ title }}</h5>
    </div>
    <div class="card-body">
      <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        {{ form|crispy }}
        <div class="mt-3">
          <button type="submit" class="btn btn-primary">Enregistrer</button>
          <a href="{% url 'admin_courses' %}" class="btn btn-secondary">Annuler</a>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
