from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from users.models import User
from courses.models import Course, Module

def admin_dashboard(request):
    # Vérifier que l'utilisateur est admin
    if not request.user.is_authenticated or not request.user.is_staff:
        return redirect('home')
        
    # Récupérer les statistiques
    student_count = User.objects.filter(role='student').count()
    instructor_count = User.objects.filter(role='instructor').count()
    course_count = Course.objects.count()
    module_count = Module.objects.count()
    
    context = {
        'student_count': student_count,
        'instructor_count': instructor_count,
        'course_count': course_count,
        'module_count': module_count,
    }
    
    return render(request, 'admin/dashboard.html', context)

@login_required
@user_passes_test(lambda u: u.is_staff)
def user_list(request):
    users = User.objects.all()
    return render(request, 'admin/user_list.html', {'users': users})

@login_required
@user_passes_test(lambda u: u.is_staff)
def course_list(request):
    courses = Course.objects.all()
    return render(request, 'admin/course_list.html', {'courses': courses})

@login_required
@user_passes_test(lambda u: u.is_staff)
def settings(request):
    return render(request, 'admin/settings.html')

