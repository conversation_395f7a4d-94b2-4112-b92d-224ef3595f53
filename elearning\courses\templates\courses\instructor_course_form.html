{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
  .form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .form-header {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .form-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }

  .form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
  }

  .form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .section-title {
    color: #333;
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
  }

  .form-control:focus, .form-select:focus {
    border-color: #C8A8E9;
    box-shadow: 0 0 0 0.2rem rgba(200, 168, 233, 0.25);
  }

  .form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .btn-submit {
    background: linear-gradient(135deg, #C8A8E9, #A8D0F0);
    color: #333;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #333;
  }

  .btn-cancel {
    background: #6c757d;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 1rem;
    transition: all 0.3s ease;
  }

  .btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
  }

  .image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 10px;
    margin-top: 1rem;
  }

  .help-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
  }

  .required-field::after {
    content: " *";
    color: #dc3545;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
  }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
  <div class="form-header">
    <h1><i class="fas fa-book-open"></i> {{ title }}</h1>
    <p>Créez un cours engageant pour vos étudiants</p>
  </div>

  <div class="form-card">
    <form method="post" enctype="multipart/form-data">
      {% csrf_token %}
      
      <!-- Informations de base -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-info-circle"></i>
          Informations de Base
        </h3>
        
        <div class="row">
          <div class="col-md-12 mb-3">
            <label for="{{ form.title.id_for_label }}" class="form-label required-field">Titre du cours</label>
            {{ form.title }}
            {% if form.title.help_text %}
              <div class="help-text">{{ form.title.help_text }}</div>
            {% endif %}
            {% if form.title.errors %}
              <div class="text-danger">{{ form.title.errors }}</div>
            {% endif %}
          </div>
          
          <div class="col-md-12 mb-3">
            <label for="{{ form.description.id_for_label }}" class="form-label required-field">Description</label>
            {{ form.description }}
            {% if form.description.help_text %}
              <div class="help-text">{{ form.description.help_text }}</div>
            {% endif %}
            {% if form.description.errors %}
              <div class="text-danger">{{ form.description.errors }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Catégorie et Niveau -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-tags"></i>
          Catégorie et Niveau
        </h3>
        
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="{{ form.category.id_for_label }}" class="form-label">Catégorie</label>
            {{ form.category }}
            {% if form.category.errors %}
              <div class="text-danger">{{ form.category.errors }}</div>
            {% endif %}
          </div>
          
          <div class="col-md-6 mb-3">
            <label for="{{ form.level.id_for_label }}" class="form-label">Niveau</label>
            {{ form.level }}
            {% if form.level.errors %}
              <div class="text-danger">{{ form.level.errors }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Image et Durée -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-image"></i>
          Image et Durée
        </h3>
        
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="{{ form.image.id_for_label }}" class="form-label">Image du cours</label>
            {{ form.image }}
            {% if form.image.help_text %}
              <div class="help-text">{{ form.image.help_text }}</div>
            {% endif %}
            {% if form.image.errors %}
              <div class="text-danger">{{ form.image.errors }}</div>
            {% endif %}
            {% if course and course.image %}
              <img src="{{ course.image.url }}" alt="Image actuelle" class="image-preview">
            {% endif %}
          </div>
          
          <div class="col-md-6 mb-3">
            <label for="{{ form.duration.id_for_label }}" class="form-label">Durée estimée</label>
            {{ form.duration }}
            {% if form.duration.help_text %}
              <div class="help-text">{{ form.duration.help_text }}</div>
            {% endif %}
            {% if form.duration.errors %}
              <div class="text-danger">{{ form.duration.errors }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Publication -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-globe"></i>
          Publication
        </h3>
        
        <div class="form-check">
          {{ form.is_published }}
          <label class="form-check-label" for="{{ form.is_published.id_for_label }}">
            Publier ce cours (les étudiants pourront s'y inscrire)
          </label>
          {% if form.is_published.errors %}
            <div class="text-danger">{{ form.is_published.errors }}</div>
          {% endif %}
        </div>
      </div>

      <!-- Actions -->
      <div class="form-actions">
        <a href="{% url 'instructor_my_courses' %}" class="btn-cancel">
          <i class="fas fa-times"></i>
          Annuler
        </a>
        <button type="submit" class="btn-submit">
          <i class="fas fa-save"></i>
          {% if course %}Mettre à jour{% else %}Créer le cours{% endif %}
        </button>
      </div>
    </form>
  </div>
</div>

<script>
// Prévisualisation de l'image
document.getElementById('{{ form.image.id_for_label }}').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            let preview = document.querySelector('.image-preview');
            if (!preview) {
                preview = document.createElement('img');
                preview.className = 'image-preview';
                e.target.parentNode.appendChild(preview);
            }
            preview.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});
</script>
{% endblock %}
