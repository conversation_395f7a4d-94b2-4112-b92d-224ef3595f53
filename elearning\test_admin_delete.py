#!/usr/bin/env python
"""
Script de test pour la suppression de cours dans l'espace admin
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

from django.contrib.auth import get_user_model
from courses.models import Course
from decimal import Decimal

User = get_user_model()

def test_admin_delete():
    print("=== TEST SUPPRESSION COURS ADMIN ===\n")
    
    # 1. Vérifier qu'il y a un admin
    admin_users = User.objects.filter(role='admin')
    if not admin_users.exists():
        print("❌ Aucun utilisateur admin trouvé!")
        print("Création d'un admin de test...")
        admin = User.objects.create_user(
            username='admin_test',
            email='<EMAIL>',
            password='admin123',
            role='admin'
        )
        print(f"✅ Admin créé: {admin.username}")
    else:
        admin = admin_users.first()
        print(f"✅ Admin trouvé: {admin.username}")
    
    # 2. Créer un cours de test pour la suppression
    print("\n2. CRÉATION D'UN COURS DE TEST:")
    
    # Trouver un instructeur
    instructor = User.objects.filter(role='instructor').first()
    if not instructor:
        instructor = User.objects.create_user(
            username='instructor_test',
            email='<EMAIL>',
            password='instructor123',
            role='instructor'
        )
        print(f"✅ Instructeur créé: {instructor.username}")
    else:
        print(f"✅ Instructeur trouvé: {instructor.username}")
    
    # Créer le cours de test
    test_course = Course.objects.create(
        title="Cours de Test - À Supprimer",
        description="Ce cours sera supprimé pour tester la fonctionnalité",
        instructor=instructor,
        category='programming',
        level='beginner',
        is_published=True,
        price=Decimal('29.99'),
        is_free=False
    )
    
    print(f"✅ Cours de test créé: {test_course.title} (ID: {test_course.id})")
    
    # 3. Afficher les informations pour le test manuel
    print(f"\n3. INFORMATIONS POUR LE TEST:")
    print(f"   - URL de suppression: http://127.0.0.1:8000/admin-dashboard/courses/{test_course.id}/delete/")
    print(f"   - Connectez-vous avec: {admin.username} / admin123")
    print(f"   - Ou allez sur: http://127.0.0.1:8000/admin-dashboard/courses/")
    
    # 4. Lister tous les cours existants
    print(f"\n4. COURS EXISTANTS:")
    courses = Course.objects.all()
    for course in courses:
        print(f"   - {course.title} (ID: {course.id}) - Instructeur: {course.instructor.username}")
    
    print(f"\n✅ SETUP TERMINÉ!")
    print(f"Vous pouvez maintenant tester la suppression du cours ID {test_course.id}")
    
    return test_course.id

if __name__ == "__main__":
    test_admin_delete()
