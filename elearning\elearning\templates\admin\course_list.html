{% extends 'base.html' %}
{% load static %}

{% block title %}Gestion des Cours | E-Learn+{% endblock %}

{% block content %}
<div class="container">
  <div class="admin-header">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-6">
          <h1 class="admin-title">
            <i class="fas fa-book me-2"></i>
            Gestion des Cours
          </h1>
          <p class="admin-welcome">Gérez les cours de votre plateforme d'apprentissage.</p>
        </div>
        <div class="col-md-6">
          <div class="admin-nav">
            <a href="{% url 'dashboard' %}" class="admin-nav-btn">
              <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="{% url 'admin_users' %}" class="admin-nav-btn">
              <i class="fas fa-users"></i> Utilisateurs
            </a>
            <a href="{% url 'admin_settings' %}" class="admin-nav-btn">
              <i class="fas fa-cog"></i> Paramètres
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card shadow-sm">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Liste des cours</h5>
      <a href="{% url 'create_course' %}" class="btn btn-primary btn-sm">
        <i class="fas fa-plus"></i> Ajouter un cours
      </a>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Titre</th>
              <th>Catégorie</th>
              <th>Instructeur</th>
              <th>Date de création</th>
              <th>Étudiants</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for course in courses %}
            <tr>
              <td>{{ course.title }}</td>
              <td>{{ course.get_category_display }}</td>
              <td>{{ course.instructor.username }}</td>
              <td>{{ course.created_at|date:"d/m/Y" }}</td>
              <td>{{ course.students.count }}</td>
              <td>
                <div class="btn-group">
                  <a href="{% url 'update_course' course.id %}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-edit"></i>
                  </a>
                  <a href="{% url 'delete_course' course.id %}" class="btn btn-sm btn-outline-danger">
                    <i class="fas fa-trash"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="6" class="text-center">Aucun cours trouvé</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{% endblock %}