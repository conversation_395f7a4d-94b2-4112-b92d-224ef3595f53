#!/usr/bin/env python
"""
Script pour exécuter les migrations et configurer les données de test
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'elearning.settings')
django.setup()

from django.core.management import execute_from_command_line
from users.models import User
from courses.models import Course
from decimal import Decimal

def run_migrations():
    """Exécuter toutes les migrations"""
    print("=== EXÉCUTION DES MIGRATIONS ===")
    
    try:
        # Migrations pour les quiz
        print("Migration des quiz...")
        execute_from_command_line(['manage.py', 'makemigrations', 'quizzes'])
        
        # Migrations pour les paiements
        print("Migration des paiements...")
        execute_from_command_line(['manage.py', 'makemigrations', 'payments'])
        
        # Appliquer toutes les migrations
        print("Application de toutes les migrations...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✅ Migrations terminées avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des migrations: {e}")
        return False

def setup_test_data():
    """Configurer les données de test"""
    print("\n=== CONFIGURATION DES DONNÉES DE TEST ===")
    
    try:
        # Créer un instructeur de test
        instructor, created = User.objects.get_or_create(
            username='prof_test',
            defaults={
                'email': '<EMAIL>',
                'role': 'instructor',
                'first_name': 'Professeur',
                'last_name': 'Test'
            }
        )
        if created:
            instructor.set_password('test123')
            instructor.save()
            print(f"✅ Instructeur créé: {instructor.username}")
        else:
            print(f"ℹ️ Instructeur existe déjà: {instructor.username}")
        
        # Créer un étudiant de test
        student, created = User.objects.get_or_create(
            username='etudiant_test',
            defaults={
                'email': '<EMAIL>',
                'role': 'student',
                'first_name': 'Étudiant',
                'last_name': 'Test'
            }
        )
        if created:
            student.set_password('test123')
            student.save()
            print(f"✅ Étudiant créé: {student.username}")
        else:
            print(f"ℹ️ Étudiant existe déjà: {student.username}")
        
        # Créer des cours de test avec prix
        courses_data = [
            {
                'title': 'Python pour Débutants',
                'description': 'Apprenez les bases de Python',
                'category': 'programming',
                'level': 'beginner',
                'is_free': True,
                'price': Decimal('0.00')
            },
            {
                'title': 'Django Avancé',
                'description': 'Développement web avec Django',
                'category': 'programming',
                'level': 'advanced',
                'is_free': False,
                'price': Decimal('49.99')
            },
            {
                'title': 'Machine Learning',
                'description': 'Introduction au Machine Learning',
                'category': 'data_science',
                'level': 'intermediate',
                'is_free': False,
                'price': Decimal('79.99')
            }
        ]
        
        for course_data in courses_data:
            course, created = Course.objects.get_or_create(
                title=course_data['title'],
                defaults={
                    **course_data,
                    'instructor': instructor,
                    'is_published': True
                }
            )
            if created:
                print(f"✅ Cours créé: {course.title} ({'Gratuit' if course.is_free else f'{course.price}€'})")
            else:
                # Mettre à jour les prix si le cours existe déjà
                course.is_free = course_data['is_free']
                course.price = course_data['price']
                course.save()
                print(f"ℹ️ Cours mis à jour: {course.title} ({'Gratuit' if course.is_free else f'{course.price}€'})")
        
        print("✅ Données de test configurées avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la configuration des données: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CONFIGURATION DE L'APPLICATION E-LEARNING")
    print("=" * 50)
    
    # Exécuter les migrations
    if not run_migrations():
        sys.exit(1)
    
    # Configurer les données de test
    if not setup_test_data():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 CONFIGURATION TERMINÉE AVEC SUCCÈS!")
    print("\nVous pouvez maintenant:")
    print("1. Démarrer le serveur: python manage.py runserver")
    print("2. Vous connecter avec:")
    print("   - Instructeur: prof_test / test123")
    print("   - Étudiant: etudiant_test / test123")
    print("3. Tester les fonctionnalités de paiement et de quiz")

if __name__ == '__main__':
    main()
