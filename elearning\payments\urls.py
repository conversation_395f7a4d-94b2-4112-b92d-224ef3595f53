from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Historique des paiements
    path('history/', views.payment_history, name='payment_history'),
    
    # Paiement de cours
    path('course/<int:course_id>/', views.course_payment, name='course_payment'),
    path('course/<int:course_id>/process/', views.process_course_payment, name='process_course_payment'),
    
    # Abonnements
    path('subscription/', views.subscription_page, name='subscription_page'),
    path('subscribe/<str:subscription_type>/', views.subscribe, name='subscribe'),
    
    # Factures
    path('invoice/<int:invoice_id>/', views.invoice_detail, name='invoice_detail'),
]
